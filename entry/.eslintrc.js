module.exports = {
  root: true,
  env: {
    es2021: true,
    node: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module',
  },
  plugins: [
    '@typescript-eslint',
  ],
  rules: {
    // 禁止相对路径导入bishare模块
    'no-restricted-imports': [
      'error',
      {
        patterns: [
          {
            group: ['**/bishare/**', '../**/bishare/**', '../../**/bishare/**', '../../../**/bishare/**', '../../../../**/bishare/**', '../../../../../**/bishare/**'],
            message: '禁止使用相对路径导入bishare模块，请使用 @ohos/libbishare_napi 代替'
          }
        ]
      }
    ],
    
    // 强制使用特定的导入方式
    'import/no-relative-parent-imports': 'off', // 如果安装了eslint-plugin-import
  },
  
  // 覆盖特定文件的规则
  overrides: [
    {
      files: ['**/*.ets', '**/*.ts'],
      rules: {
        // 对于.ets和.ts文件，严格禁止相对路径导入bishare
        'no-restricted-imports': [
          'error',
          {
            patterns: [
              {
                group: ['**/bishare/**'],
                message: '❌ 禁止使用相对路径导入bishare模块！\n\n请使用标准导入方式：\n✅ import { NetworkInfoOptions, NetworkType } from \'@ohos/libbishare_napi\';\n\n而不是：\n❌ import { NetworkInfoOptions } from \'../../../../../bishare/Index\';'
              }
            ]
          }
        ]
      }
    }
  ]
};
