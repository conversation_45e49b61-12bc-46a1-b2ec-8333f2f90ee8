import AbilityStage from '@ohos.app.ability.AbilityStage';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import { InfoCmdManager, Log } from '@ohos/lib_info_sender';

/**
 * 应用的application
 */
const TAG = 'ShareAppStage';
export default class ShareAppStage extends AbilityStage{

  /**
   * 应用的HAP在首次加载的时，为该Module初始化操作
   */
  onCreate(): void{
    Log.showInfo(TAG, 'onCreate');
    InfoCmdManager.getInstance().initInfoCmd(this.context.getApplicationContext());
  }

  onMemoryLevel(level: AbilityConstant.MemoryLevel): void{
    // 根据系统可用内存的变化情况，释放不必要的内存
    if (level === AbilityConstant.MemoryLevel.MEMORY_LEVEL_CRITICAL){

    }
  }

}