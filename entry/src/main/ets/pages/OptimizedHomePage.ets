import common from '@ohos.app.ability.common';
import { UIAdapter } from '../comm/adapters/UIAdapter';
import { ExtendedDeviceInfo, DeviceConnectionStatus } from '@ohos/libbishare_napi';
import { InitOptions, BlogPriority } from '@ohos/libbishare_napi';

/**
 * 优化后的主页面
 * 展示如何使用新的架构
 */
@Entry
@Component
struct OptimizedHomePage {
  private context: common.UIAbilityContext = getContext(this) as common.UIAbilityContext;
  private uiAdapter: UIAdapter = UIAdapter.getInstance();

  // 响应式状态数据
  @State private discoveredDevices: ExtendedDeviceInfo[] = [];
  @State private connectedDevices: ExtendedDeviceInfo[] = [];
  @State private isDiscovering: boolean = false;
  @State private isInitialized: boolean = false;
  @State private statusMessage: string = '未初始化';

  aboutToAppear() {
    this.initializeService();
    this.setupDataBinding();
  }

  aboutToDisappear() {
    this.releaseService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      this.statusMessage = '正在初始化...';
      
      const options: InitOptions = {
        isConsole: true,
        isFile: false,
        filePath: '',
        priority: BlogPriority.INFO
      };

      const success = await this.uiAdapter.initialize(this.context, options);
      
      if (success) {
        this.statusMessage = '初始化成功';
        this.isInitialized = true;
      } else {
        this.statusMessage = '初始化失败';
      }
    } catch (error) {
      this.statusMessage = `初始化异常: ${error.message}`;
    }
  }

  /**
   * 释放服务
   */
  private async releaseService(): Promise<void> {
    await this.uiAdapter.release();
  }

  /**
   * 设置数据绑定
   */
  private setupDataBinding(): void {
    // 定期更新UI状态
    setInterval(() => {
      this.discoveredDevices = this.uiAdapter.getDiscoveredDevices();
      this.connectedDevices = this.uiAdapter.getConnectedDevices();
      this.isDiscovering = this.uiAdapter.getDiscoveryStatus();
      this.isInitialized = this.uiAdapter.getInitializationStatus();
    }, 1000);
  }

  /**
   * 开始设备发现
   */
  private async startDiscovery(): Promise<void> {
    const success = await this.uiAdapter.startDeviceDiscovery();
    if (success) {
      this.statusMessage = '设备发现已启动';
    } else {
      this.statusMessage = '设备发现启动失败';
    }
  }

  /**
   * 停止设备发现
   */
  private async stopDiscovery(): Promise<void> {
    const success = await this.uiAdapter.stopDeviceDiscovery();
    if (success) {
      this.statusMessage = '设备发现已停止';
    } else {
      this.statusMessage = '设备发现停止失败';
    }
  }

  /**
   * 连接设备
   */
  private async connectDevice(deviceId: string): Promise<void> {
    const success = await this.uiAdapter.connectDevice(deviceId);
    if (success) {
      this.statusMessage = `设备 ${deviceId} 连接成功`;
    } else {
      this.statusMessage = `设备 ${deviceId} 连接失败`;
    }
  }

  /**
   * 断开设备连接
   */
  private async disconnectDevice(deviceId: string): Promise<void> {
    const success = await this.uiAdapter.disconnectDevice(deviceId);
    if (success) {
      this.statusMessage = `设备 ${deviceId} 断开连接成功`;
    } else {
      this.statusMessage = `设备 ${deviceId} 断开连接失败`;
    }
  }

  /**
   * 刷新设备列表
   */
  private async refreshDevices(): Promise<void> {
    await this.uiAdapter.refreshDeviceList();
    this.statusMessage = '设备列表已刷新';
  }

  build() {
    Column() {
      // 状态栏
      Row() {
        Text('BiShare 优化版')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
        
        Blank()
        
        Text(this.statusMessage)
          .fontSize(14)
          .fontColor(this.isInitialized ? Color.Green : Color.Red)
      }
      .width('100%')
      .padding(16)
      .backgroundColor(Color.White)

      // 控制按钮
      Row() {
        Button(this.isDiscovering ? '停止发现' : '开始发现')
          .enabled(this.isInitialized)
          .onClick(() => {
            if (this.isDiscovering) {
              this.stopDiscovery();
            } else {
              this.startDiscovery();
            }
          })

        Button('刷新设备')
          .enabled(this.isInitialized)
          .onClick(() => this.refreshDevices())
          .margin({ left: 16 })
      }
      .width('100%')
      .padding(16)

      // 已连接设备
      if (this.connectedDevices.length > 0) {
        Column() {
          Text(`已连接设备 (${this.connectedDevices.length})`)
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .margin({ bottom: 8 })

          ForEach(this.connectedDevices, (device: ExtendedDeviceInfo) => {
            this.DeviceItem(device, true)
          })
        }
        .width('100%')
        .padding(16)
        .backgroundColor('#f0f0f0')
      }

      // 已发现设备
      Column() {
        Text(`已发现设备 (${this.discoveredDevices.length})`)
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 8 })

        if (this.discoveredDevices.length === 0) {
          Text(this.isDiscovering ? '正在搜索设备...' : '暂无发现设备')
            .fontSize(14)
            .fontColor(Color.Gray)
            .margin(16)
        } else {
          ForEach(this.discoveredDevices, (device: ExtendedDeviceInfo) => {
            this.DeviceItem(device, false)
          })
        }
      }
      .width('100%')
      .padding(16)
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }

  /**
   * 设备项组件
   */
  @Builder
  DeviceItem(device: ExtendedDeviceInfo, isConnected: boolean) {
    Row() {
      Column() {
        Text(device.deviceName || device.deviceId)
          .fontSize(16)
          .fontWeight(FontWeight.Medium)
        
        Text(`${device.deviceType} - ${device.deviceId}`)
          .fontSize(12)
          .fontColor(Color.Gray)
        
        if (device.connectionCount && device.connectionCount > 0) {
          Text(`连接次数: ${device.connectionCount}`)
            .fontSize(10)
            .fontColor(Color.Blue)
        }
      }
      .alignItems(HorizontalAlign.Start)
      .layoutWeight(1)

      // 状态指示器
      Circle()
        .width(12)
        .height(12)
        .fill(this.getStatusColor(device.connectionStatus))
        .margin({ right: 8 })

      // 操作按钮
      if (isConnected) {
        Button('断开')
          .fontSize(12)
          .onClick(() => this.disconnectDevice(device.deviceId))
      } else if (device.connectionStatus === DeviceConnectionStatus.DISCONNECTED) {
        Button('连接')
          .fontSize(12)
          .onClick(() => this.connectDevice(device.deviceId))
      } else {
        Button('连接中...')
          .fontSize(12)
          .enabled(false)
      }
    }
    .width('100%')
    .padding(12)
    .margin({ bottom: 8 })
    .backgroundColor(Color.White)
    .borderRadius(8)
  }

  /**
   * 获取状态颜色
   */
  private getStatusColor(status: DeviceConnectionStatus): Color {
    switch (status) {
      case DeviceConnectionStatus.CONNECTED:
        return Color.Green;
      case DeviceConnectionStatus.CONNECTING:
        return Color.Orange;
      case DeviceConnectionStatus.FAILED:
        return Color.Red;
      default:
        return Color.Gray;
    }
  }
}
