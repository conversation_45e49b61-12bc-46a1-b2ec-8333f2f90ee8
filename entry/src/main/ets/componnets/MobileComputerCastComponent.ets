import { CommonConnStepBuilder } from '../builder/CommonConnStepBuilder';


/**
 * 手机/电脑投屏
 */
@Component
export struct MobileComputerCastComponent {

  /**
   * 投屏网络
   */
  @State castNet: string = 'BOE_1234';

  /**
   * 密码
   */
  @State pwd: string = 'BOE888888_';

  /**
   * 下载链接
   */
  @State downloadUrl: string = '';


  constructor(downloadUrl: string, castNet: string, pwd: string) {
    super();
    this.downloadUrl = downloadUrl;
    this.castNet = castNet;
    this.pwd = pwd;
  }

  build() {
    Column() {
      Text($r('app.string.mobile_computer_cast'))
        .fontSize($r('app.float.fp_28'))
        .margin({
          top: $r('app.float.vp_24'),
          bottom: $r('app.float.vp_32')
        })

      CommonConnStepBuilder($r('app.string.download_software')
        , $r('app.string.connect_network')
        , $r('app.string.start_cast'))

      Row() {
        this.DownloadSoftBuilder()
        this.ConnNetBuilder()
        this.StartCastBuilder()
      }.alignItems(VerticalAlign.Top)
      .margin({
        left: $r('app.float.vp_48'),
        right: $r('app.float.vp_48')
      })
    }
  }

  /**
   * 下载软件
   */
  @Builder
  DownloadSoftBuilder() {
    Column() {
      Stack() {
        Image($r('app.media.bg_download_software'))
          .width($r('app.float.vp_240'))
          .height($r('app.float.vp_138'))
        Text(this.downloadUrl)
          .fontSize($r('app.float.fp_14'))
          .fontColor($r('app.color.color_0081FF'))
      }.margin({
        bottom: $r('app.float.vp_8')
      })
      Text($r('app.string.scan_input_download'))
        .fontSize($r('app.float.fp_16'))
        .fontColor($r('app.color.color_787A80'))
    }
    .margin({
      right: $r('app.float.vp_42')
    })
  }

  /**
   * 连接网络
   */
  @Builder
  ConnNetBuilder() {
    Column() {
      Stack() {
        Image($r('app.media.bg_conn_net'))
          .width($r('app.float.vp_240'))
          .height($r('app.float.vp_138'))
        Column() {
          Text($r('app.string.cast_network'))
            .fontSize($r('app.float.fp_10'))
            .fontColor($r('app.color.color_303133'))
            .margin({bottom: $r('app.float.vp_8')})
          Text(this.castNet)
            .fontSize($r('app.float.fp_14'))
            .fontColor($r('app.color.color_0081FF'))
            .fontWeight(FontWeight.Bold)
            .margin({bottom: $r('app.float.vp_8')})
          Text($r('app.string.password'))
            .fontSize($r('app.float.fp_10'))
            .fontColor($r('app.color.color_303133'))
            .margin({bottom: $r('app.float.vp_8')})
          Text(this.pwd)
            .fontSize($r('app.float.fp_14'))
            .fontColor($r('app.color.color_0081FF'))
            .fontWeight(FontWeight.Bold)
        }.alignItems(HorizontalAlign.Start)
        .margin({
          left: $r('app.float.vp_77'),
          top: $r('app.float.vp_31')
        })
      }.alignContent(Alignment.TopStart)
      .width($r('app.float.vp_240'))
      .height($r('app.float.vp_138'))
      .margin({
        bottom: $r('app.float.vp_8')
      })
      Text($r('app.string.conn_to_cast_network'))
        .fontSize($r('app.float.fp_16'))
        .fontColor($r('app.color.color_787A80'))
    }.width($r('app.float.vp_240'))
    .margin({
      right: $r('app.float.vp_42')
    })
  }

  /**
   * 开始投屏
   */
  @Builder
  StartCastBuilder() {
    Column() {
      Image($r('app.media.bg_start_cast'))
        .width($r('app.float.vp_240'))
        .height($r('app.float.vp_138'))
        .margin({
          bottom: $r('app.float.vp_8')
        })
      Text($r('app.string.choose_cast_method'))
        .fontSize($r('app.float.fp_16'))
        .fontColor($r('app.color.color_787A80'))
    }
  }

}