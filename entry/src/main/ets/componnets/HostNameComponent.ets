

/**
 * 主机名
 */
@Component
export struct HostNameComponent {

  /**
   * 本机名称
   */
  @State deviceName: string = 'C100一体机'

  constructor(deviceName: string) {
    super();
    this.deviceName = deviceName;
  }

  build() {
    Row() {
      Text($r('app.string.host_name'))
        .fontSize($r('app.float.fp_20'))
        .fontColor($r('app.color.color_606266'))
      Text(this.deviceName)
        .fontSize($r('app.float.fp_24'))
        .fontColor($r('app.color.color_303133'))
        .fontWeight(FontWeight.Bold)
        .margin({right: $r('app.float.vp_4')})
      Image($r('app.media.ic_modify'))
        .width($r('app.float.vp_32'))
        .height($r('app.float.vp_32'))
        .onClick(() => {
          //todo 编辑本机名称
        })
    }
  }

}