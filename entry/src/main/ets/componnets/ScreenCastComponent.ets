import { CommonConnStepBuilder } from '../builder/CommonConnStepBuilder';


/**
 * 投屏器投屏
 */
@Component
export struct ScreenCastComponent {


  build() {
    Column() {
      Text($r('app.string.screen_cast'))
        .fontSize($r('app.float.fp_28'))
        .margin({
          top: $r('app.float.vp_24'),
          bottom: $r('app.float.vp_32')
        })
      CommonConnStepBuilder($r('app.string.insert_large_screen')
        , $r('app.string.insert_computer')
        , $r('app.string.press_key'))
      Row({space: '42vp'}) {
        //插入大屏
        Column() {
          Image($r("app.media.bg_insert_large_screen"))
            .width($r('app.float.vp_240'))
            .height($r('app.float.vp_138'))
            .margin({bottom: $r('app.float.vp_6')})
          Text($r('app.string.wait_five_seconds_for_match'))
            .fontSize($r('app.float.fp_16'))
            .fontColor($r('app.color.color_787A80'))
        }
        //插入电脑
        Column() {
          Image($r("app.media.bg_insert_computer"))
            .width($r('app.float.vp_240'))
            .height($r('app.float.vp_138'))
            .margin({bottom: $r('app.float.vp_6')})
          Text($r('app.string.green_light_conn_success'))
            .fontSize($r('app.float.fp_16'))
            .fontColor($r('app.color.color_787A80'))
        }
        //按下按键
        Column() {
          Image($r("app.media.bg_press_key"))
            .width($r('app.float.vp_240'))
            .height($r('app.float.vp_138'))
            .margin({bottom: $r('app.float.vp_6')})
          Text($r('app.string.will_see_cast_screen'))
            .fontSize($r('app.float.fp_16'))
            .fontColor($r('app.color.color_787A80'))
        }
      }
    }
  }

}