

/**
 * 设置 - 投屏水印
 */
@Component
export struct CastWaterSettingComponent {

  @Consume('settingStack') settingStack: NavPathStack;

    build() {
      NavDestination() {
        Column() {
          //投屏水印 - 帮助
          this.WaterHelpBuilder()

          //水印模式
          this.waterPatternBuilder()

          //水印内容
          this.waterContentBuilder()
        }
        .padding($r('app.float.vp_16'))
      }
      .hideTitleBar(true)
      .borderRadius($r('app.float.vp_16'))
      .width($r('app.float.vp_424'))
    }

  @Builder
  WaterHelpBuilder() {
    Row() {
      Row() {
        Image($r('app.media.ic_back_grey'))
        .width($r('app.float.vp_32'))
        .height($r('app.float.vp_32'))
        .margin({right: $r('app.float.vp_8')})
          .onClick(() => {
            this.settingStack.pop()
          })

        Text($r('app.string.cast_water'))
          .fontSize($r('app.float.vp_24'))
          .fontColor($r('app.color.color_303133'))
          .fontWeight(FontWeight.Bold)
      }
      .width('100%')
      .justifyContent(FlexAlign.Start)

      Row() {
        Image($r('app.media.ic_help'))
          .width($r('app.float.vp_20'))
          .height($r('app.float.vp_20'))
        Text($r('app.string.help'))
          .fontSize($r('app.float.vp_16'))
          .fontColor($r('app.color.color_606266'))
          .margin({left: $r('app.float.vp_1')})
      }.onClick(() => {
        //todo 待实现“帮助”点击

      })
    }
    .width('100%')
    .justifyContent(FlexAlign.SpaceBetween)
    .margin({
      bottom: $r('app.float.vp_16'),
    })
  }

  /**
   * 水印模式
   */
  @Builder
  waterPatternBuilder() {
    Column() {
      Text($r('app.string.water_pattern'))
        .fontSize($r('app.float.fp_16'))
        .fontColor($r('app.color.color_606266'))
        .margin({bottom: $r('app.float.vp_8')})

      Row() {
        Text($r('app.string.show_water'))
          .fontSize($r('app.float.fp_20'))
          .fontColor($r('app.color.color_303133'))
        Image($r('app.media.ic_tick_light'))
          .width($r('app.float.vp_24'))
          .height($r('app.float.vp_24'))
      }
      .padding({
        left: $r('app.float.vp_16'),
        right: $r('app.float.vp_16')
      })
      .width('100%')
      .height($r('app.float.vp_76'))
      .backgroundColor($r('app.color.color_FAFAFA'))
      .justifyContent(FlexAlign.SpaceBetween)
    }
  }

  /**
   * 水印内容
   */
  @Builder
  waterContentBuilder() {
    Column() {
      Text($r('app.string.water_pattern'))
        .fontSize($r('app.float.fp_16'))
        .fontColor($r('app.color.color_606266'))
        .margin({bottom: $r('app.float.vp_8')})

      Column() {
        Row() {
          Text($r('app.string.show_water'))
            .fontSize($r('app.float.fp_20'))
            .fontColor($r('app.color.color_303133'))
          Image($r('app.media.ic_tick_light'))
            .width($r('app.float.vp_24'))
            .height($r('app.float.vp_24'))
        }
        .padding({
          left: $r('app.float.vp_16'),
          right: $r('app.float.vp_16')
        })
        .width('100%')
        .height($r('app.float.vp_76'))
        .backgroundColor($r('app.color.color_FAFAFA'))
        .justifyContent(FlexAlign.SpaceBetween)

        //todo
      }
    }
  }

}