import router from '@ohos.router';
import { CustomSettingDialogComponent } from '../view/dialog/CustomSettingDialogComponent';
import { UIAdapter } from '../comm/adapters/UIAdapter';


/**
 * 文件接收/设置
 */
@Component
export struct BottomComponent {

  @State isPopupShow: boolean = false
  private uiAdapter: UIAdapter = UIAdapter.getInstance();

  // popup构造器定义弹框内容
  @Builder popupBuilder() {
      CustomSettingDialogComponent()
  }

  build() {
    Row() {
      //文件接收
      Row() {
        Image($r('app.media.ic_file_receiving'))
          .width($r('app.float.vp_24'))
          .height($r('app.float.vp_24'))
          .margin({
            left: $r('app.float.vp_20'),
            right: $r('app.float.vp_4')
          })
        Text($r('app.string.file_receive'))
          .fontSize($r('app.float.fp_16'))
          .fontColor($r('app.color.color_303133'))
      }
      .width($r('app.float.vp_132'))
      .height($r('app.float.vp_40'))
      .borderRadius($r('app.float.vp_25'))
      .backgroundColor($r('app.color.color_EBEDF0'))
      .margin({right: $r('app.float.vp_16')})
      .onClick(async () => {
        // 获取已发现的设备
        const devices = this.uiAdapter.getDiscoveredDevices();
        console.log('已发现设备:', devices.length);
      })

      //设置
        Row() {
          Image($r('app.media.ic_setting'))
            .width($r('app.float.vp_18'))
            .height($r('app.float.vp_18'))
            .margin({
              left: $r('app.float.vp_39'),
              right: $r('app.float.vp_4')
            })
          Text($r('app.string.setting'))
            .fontSize($r('app.float.fp_16'))
            .fontColor($r('app.color.color_303133'))
        }
        .width($r('app.float.vp_132'))
        .height($r('app.float.vp_40'))
        .borderRadius($r('app.float.vp_25'))
        .backgroundColor($r('app.color.color_EBEDF0'))
        .bindPopup(this.isPopupShow, {
          builder: this.popupBuilder,
          placement: Placement.Top,
          popupColor: $r('app.color.color_EBEDF0'),
          enableArrow: false,
          targetSpace: $r('app.float.vp_24'),
          onStateChange: (e) => {
            if (!e.isVisible) {
              this.isPopupShow = false
            }
          }
        })
        // .position({ x: 0, y: $r('app.float.vp_24') }) //设置这个的话，会导致 “文件接收”按钮在“设置”按钮下面
        .onClick((event: ClickEvent) => {
          this.isPopupShow = !this.isPopupShow
        })

      //关闭图标
      Column() {
        Image($r('app.media.ic_close'))
          .width($r('app.float.vp_40'))
          .height($r('app.float.vp_40'))
          .margin({
            left: $r('app.float.vp_184'),
            right: $r('app.float.vp_40')
          }).onClick(() => {
            router.back()
        })
      }
    }.margin({
      top: $r('app.float.vp_24'),
      left: $r('app.float.vp_1380')
    })
  }

}

