
/**
 * 投屏码
 */
@Component
export struct CastComponent {

  /**
   * 投屏码
   */
  @State castCodeArr: string[] = ["-", "-", "-", "-", "-", "-"];

  constructor(codeAttr: string[]) {
    super();
    this.castCodeArr = codeAttr;
  }

  build() {
    Row() {
      Text($r('app.string.cast_code'))
        .fontSize($r('app.float.fp_32'))
        .height($r('app.float.vp_50'))
        .fontColor($r('app.color.color_0081FF'))
        .margin({
          right: $r('app.float.vp_16')
        })

      ForEach(this.castCodeArr, (code: string) => {
        Text(code)
          .fontSize($r('app.float.fp_32'))
          .width($r('app.float.vp_44'))
          .height($r('app.float.vp_50'))
          .fontColor($r('app.color.color_0081FF'))
          .backgroundColor($r('app.color.color_EBEDF0'))
          .textAlign(TextAlign.Center)
          .margin({
            right: $r('app.float.vp_8'),
            bottom: $r('app.float.vp_16')
          })
      })
    }
  }

}