

/**
 * 网页投屏
 */
@Component
export struct WebCastComponent {

  private websiteAddr = 'iapp.boe.com/cast/';

  /**
   * 连接码
   */
  @State connCode: string = '123456789'

  constructor(websiteAddr: string, connCode: string) {
    super();
    this.websiteAddr = websiteAddr;
    this.connCode = connCode;
  }

  build() {
    Column() {
      //网页投屏/访客推荐
      this.TitleBuilder()
      //序号
      this.StepBuilder()
      //电脑输入网址 - 输入连接码
      this.InputWebSiteAndConnCode()

      Row() {
        //网址
        this.WebSiteBuilder()
        //获取连接码
        this.GetConnCode()
      }
      .margin({
        left: $r('app.float.vp_47'),
        right: $r('app.float.vp_91')
      })

      //温馨提示
      this.WarmReminder()
    }
  }

  /**
   * 网页投屏/访客推荐
   */
  @Builder
  TitleBuilder() {
    Row() {
      Text($r('app.string.web_cast'))
        .fontSize($r('app.float.fp_28'))
        .fontColor($r('app.color.color_303133'))
        .margin({right: $r('app.float.vp_12')})
      Stack() {
        Image($r('app.media.bg_visitor_recommend'))
          .width($r('app.float.vp_88'))
          .height($r('app.float.vp_32'))
        Text($r('app.string.visitor_recommendations'))
          .fontSize($r('app.float.fp_16'))
          .fontColor($r('app.color.color_0081FF'))
      }
    }.margin({top: $r('app.float.vp_24')})
  }

  /**
   * 步骤
   */
  @Builder
  StepBuilder() {
    Row() {
      Stack() {
        Circle()
          .width($r('app.float.vp_32'))
          .height($r('app.float.vp_32'))
          .fill($r('app.color.color_EBEDF0'))
        Text($r('app.string.one'))
          .fontSize($r('app.float.fp_14'))
          .fontColor($r('app.color.color_606266'))
      }.margin({
        left: $r('app.float.vp_123'),
        right: $r('app.float.vp_29')
      })
      Divider()
        .vertical(false)
        .color($r('app.color.color_DCDFE6'))
        .width($r('app.float.vp_104'))
        .height($r('app.float.vp_1'))
        .margin({
          right: $r('app.float.vp_33')
        })

      Stack() {
        Circle()
          .width($r('app.float.vp_32'))
          .height($r('app.float.vp_32'))
          .fill($r('app.color.color_EBEDF0'))
        Text($r('app.string.two'))
          .fontSize($r('app.float.fp_14'))
          .fontColor($r('app.color.color_606266'))
      }.margin({
        right: $r('app.float.vp_123')
      })
    }.margin({
      top: $r('app.float.vp_32'),
      bottom: $r('app.float.vp_12')
    })
  }

  /**
   * 电脑输入网址 - 输入连接码
   */
  @Builder
  InputWebSiteAndConnCode() {
    Row() {
      Text($r('app.string.computer_input_website_address'))
        .fontSize($r('app.float.fp_20'))
        .fontColor($r('app.color.color_303133'))
        .fontWeight(FontWeight.Bold)
        .margin({
          right: $r('app.float.vp_86')
        })
      Text($r('app.string.input_conn_code'))
        .fontSize($r('app.float.fp_20'))
        .fontColor($r('app.color.color_303133'))
        .fontWeight(FontWeight.Bold)
    }
    .margin({
      bottom: $r('app.float.vp_28')
    })
  }

  /**
   * 网址
   */
  @Builder
  WebSiteBuilder() {
    Row() {
      Image($r('app.media.ic_website'))
        .width($r('app.float.vp_24'))
        .height($r('app.float.vp_24'))
        .margin({
          right: $r('app.float.vp_2')
        })
      Text(this.websiteAddr)
        .fontSize($r('app.float.fp_16'))
        .fontColor($r('app.color.color_0081FF'))
    }.margin({
      right: $r('app.float.vp_58')
    })
  }

  /**
   * 获取连接码
   */
  @Builder
  GetConnCode() {
    Column() {
      Stack() {
        Image($r('app.media.bg_get_code'))
          .width($r('app.float.vp_96'))
          .height($r('app.float.vp_32'))
        if (this.connCode) {
          Row() {
            Text($r('app.string.get'))
              .fontSize($r('app.float.fp_16'))
              .fontColor($r('app.color.color_0081FF'))
              .margin({
                left: $r('app.float.vp_22'),
                right: $r('app.float.vp_4')
              })
            Image($r('app.media.ic_refresh'))
              .width($r('app.float.vp_28'))
              .height($r('app.float.vp_28'))
          }.width($r('app.float.vp_96'))
          .height($r('app.float.vp_32'))
          .onClick(() => {
            //todo 获取连接码
          })
        } else {
          Row() {
            Image($r('app.media.ic_get_conn_code'))
              .width($r('app.float.vp_20'))
              .height($r('app.float.vp_20'))
              .margin({
                left: $r('app.float.vp_20'),
                right: $r('app.float.vp_4')
              })
            Text($r('app.string.get'))
              .fontSize($r('app.float.fp_16'))
              .fontColor($r('app.color.color_0081FF'))
          }.width($r('app.float.vp_96'))
          .height($r('app.float.vp_32'))
        }
      }
    }.margin({left: $r('app.float.vp_20')})
  }

  /**
   * 温馨提示
   */
  @Builder
  WarmReminder() {
    Stack() {
      Image($r('app.media.bg_warm_reminder'))
        .width($r('app.float.vp_380'))
        .height($r('app.float.vp_72'))

      Column() {
        Row() {
          Image($r('app.media.ic_reminder'))
            .width($r('app.float.vp_24'))
            .height($r('app.float.vp_24'))
          Text($r('app.string.warm_reminder'))
            .fontSize($r('app.float.fp_16'))
            .fontColor($r('app.color.color_FF7B00'))
            .fontWeight(FontWeight.Bold)
        }
        .margin({
          left: $r('app.float.vp_12'),
          right: $r('app.float.vp_4'),
          top: $r('app.float.vp_12'),
          bottom: $r('app.float.vp_10')
        })

        Text($r('app.string.make_sure_computer_conn'))
          .fontSize($r('app.float.fp_16'))
          .fontColor($r('app.color.color_787A80'))
          .margin({left: $r('app.float.vp_40')})
      }.alignItems(HorizontalAlign.Start)

    }.alignContent(Alignment.TopStart)
    .margin({
      top: $r('app.float.vp_47'),
      bottom: $r('app.float.vp_48')
    })
  }

}