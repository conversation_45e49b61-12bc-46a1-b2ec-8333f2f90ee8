import { ConnectDeviceBean } from '../bean/ConnectDeviceBean'
import { StringUtils } from '../utils/StringUtils'


/**
 * 设备连接(设备发现/连接)
 */
@Component
export struct ConnectComponent {

  /**
   * 连接设备数量
   */
  @State connDeviceList: ConnectDeviceBean[] = [new ConnectDeviceBean()]

  @State connectGridColumns: string = '1fr 1fr';

  constructor(connDeviceList: ConnectDeviceBean[]) {
    super();
    this.connDeviceList = connDeviceList;
  }


  build() {
    Column() {
      if (this.connDeviceList && this.connDeviceList.length > 0) {
        this.ConnDeviceBuilder()
      } else {
        this.UnConnBuilder()
      }
    }
  }

  /**
   * 已连接设备UI
   */
  @Builder
  ConnDeviceBuilder() {
    Column() {
      Row() {
        Text($r('app.string.conn_devices'))
          .fontSize($r('app.float.vp_20'))
          .fontColor($r('app.color.color_303133'))

        Row() {
          Text($r('app.string.more'))
            .fontSize($r('app.float.vp_20'))
            .fontColor($r('app.color.color_0081FF'))
            .margin({right: $r('app.float.vp_4')})
          Image($r("app.media.ic_forward_blue"))
            .width($r('app.float.vp_20'))
            .height($r('app.float.vp_20'))
        }.onClick(() => {
          //todo 待实现“更多”点击
        })
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .margin({
        bottom: $r('app.float.vp_8'),
      })

      Column() {
        Grid() {
          ForEach(this.connDeviceList, (deviceBean: ConnectDeviceBean) => {
            GridItem() {
              Column() {
                Image($r('app.media.bg_mobile_connected'))
                  .width($r('app.float.vp_102'))
                  .height($r('app.float.vp_102'))
                  .margin({
                    bottom: $r('app.float.vp_8')
                  })
                Text($r('app.string.type_mobile'))
                  .fontSize($r('app.float.vp_16'))
                  .fontColor($r('app.color.color_303133'))
                  .margin({
                    bottom: $r('app.float.vp_8')
                  })
                Text($r('app.string.conn_times'))
                  .fontSize($r('app.float.vp_16'))
                  .fontColor($r('app.color.color_787A80'))
              }
            }
            .width($r('app.float.vp_128'))
            .onClick(() => {
              //todo 点击事件
            })
          });
        }
        .rowsTemplate(this.getRowTemplate())
        .columnsTemplate(this.connectGridColumns)
        .onAppear(() => this.adjustConnectDeviceLayout())
        .rowsGap('16vp')
        .maxCount(4)
        .minCount(1)
        .scrollBar(BarState.Off)
      }.width('100%')
      .padding({bottom: $r('app.float.vp_12')})
    }
    .width('100%')
    .alignItems(HorizontalAlign.Start)
  }

  private getRowTemplate(): string {
    const count = this.connDeviceList.length;
    if (count <= 2) {
      return '1fr'; // 单行显示
    } else {
      return '1fr 1fr'; // 两行显示（3或4条数据时）
    }
  }

  private adjustConnectDeviceLayout(): void {
    if (this.connDeviceList.length === 1) {
      this.connectGridColumns = '1fr'; // 单列居中
    } else if (this.connDeviceList.length === 2) {
      this.connectGridColumns = '1fr 1fr'; // 双列水平居中
    } else {
      this.connectGridColumns = '1fr 1fr'; // 保持双列，多行显示
    }
  }


  /**
   * 未连接设备UI
   */
  @Builder
  UnConnBuilder() {
    Column() {
      Row() {
        Text(StringUtils.format($r('app.string.conn_devices').toString(), [this.connDeviceList.length]))
          .fontSize($r('app.float.vp_20'))
          .fontColor($r('app.color.color_303133'))

        Row() {
          Text($r('app.string.more'))
            .fontSize($r('app.float.vp_20'))
            .fontColor($r('app.color.color_0081FF'))
            .margin({right: $r('app.float.vp_4')})
          Image($r("app.media.ic_forward_blue"))
            .width($r('app.float.vp_20'))
            .height($r('app.float.vp_20'))
        }.onClick(() => {
          //todo 待实现“更多”点击
        })
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .margin({
        bottom: $r('app.float.vp_8'),
      })

      Row() {
        Text($r('app.string.searching'))
          .fontSize($r('app.float.vp_16'))
          .fontColor($r('app.color.color_787A80'))
      }.width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
    }
  }

}