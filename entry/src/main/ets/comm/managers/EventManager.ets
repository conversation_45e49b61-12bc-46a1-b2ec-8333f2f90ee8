import {
  BiShareManager,
  EventType,
  EventData as NativeEventData,
  BiShareEventType,
  BiShareEventData,
  DeviceEventListener,
  RecordingEventListener,
  NetworkEventListener,
  FileEventListener,
  MessageEventListener,
  ExtendedDeviceInfo,
  DeviceConnectionStatus,
  RecordingInfo,
  RecordingStatus
} from '@ohos/libbishare_napi';
import { Log } from '@ohos/lib_info_sender';

const TAG = 'EventManager';


/**
 * 事件管理器
 * 负责：
 * 1. 统一的事件监听和分发
 * 2. 事件类型转换和路由
 * 3. 事件监听器管理
 * 4. 与原生事件系统的桥接
 */
export class EventManager {
  private biShareManager: BiShareManager;
  private deviceListeners: Set<DeviceEventListener> = new Set();
  private recordingListeners: Set<RecordingEventListener> = new Set();
  private networkListeners: Set<NetworkEventListener> = new Set();
  private fileListeners: Set<FileEventListener> = new Set();
  private messageListeners: Set<MessageEventListener> = new Set();

  // 事件回调映射
  private eventCallbacks: Map<EventType, (data: NativeEventData) => void> = new Map();
  private isInitialized: boolean = false;

  constructor() {
    this.biShareManager = BiShareManager.getInstance();
    this.setupEventCallbacks();
  }

  /**
   * 设置事件回调
   */
  private setupEventCallbacks(): void {
    // 设备信息事件
    this.eventCallbacks.set(EventType.DEVICE_INFO, (data: NativeEventData) => {
      this.handleDeviceInfoEvent(data);
    });

    // 设备信息列表事件
    this.eventCallbacks.set(EventType.DEVICE_INFO, (data: NativeEventData) => {
      this.handleDeviceListEvent(data);
    });

    // 连接状态事件
    this.eventCallbacks.set(EventType.CONNECT_STATUS, (data: NativeEventData) => {
      this.handleConnectionStatusEvent(data);
    });

    // 录制事件 - 使用设备状态事件代替
    this.eventCallbacks.set(EventType.DEVICE_STATUS, (data: NativeEventData) => {
      this.handleRecordingEvent(data);
    });
  }

  /**
   * 初始化事件管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    Log.showInfo(TAG, '事件管理器初始化');

    try {
      // 注册原生事件监听器
      this.registerNativeEventListeners();
      this.isInitialized = true;
      Log.showInfo(TAG, '事件管理器初始化成功');
    } catch (error) {
      Log.showError(TAG, '事件管理器初始化失败:', error);
      throw new Error(`事件管理器初始化失败: ${error}`);
    }
  }

  /**
   * 释放事件管理器
   */
  async release(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    Log.showInfo(TAG, '事件管理器释放');

    try {
      // 注销原生事件监听器
      this.unregisterNativeEventListeners();

      // 清理所有监听器
      this.deviceListeners.clear();
      this.recordingListeners.clear();
      this.networkListeners.clear();
      this.fileListeners.clear();
      this.messageListeners.clear();

      this.isInitialized = false;
      Log.showInfo(TAG, '事件管理器释放成功');
    } catch (error) {
      Log.showError(TAG, '事件管理器释放失败:', error);
    }
  }



  /**
   * 添加设备事件监听器
   */
  addDeviceEventListener(listener: DeviceEventListener): void {
    this.deviceListeners.add(listener);
    Log.showInfo(TAG, '添加设备事件监听器');
  }

  /**
   * 移除设备事件监听器
   */
  removeDeviceEventListener(listener: DeviceEventListener): void {
    this.deviceListeners.delete(listener);
    Log.showInfo(TAG, '移除设备事件监听器');
  }

  /**
   * 添加录制事件监听器
   */
  addRecordingEventListener(listener: RecordingEventListener): void {
    this.recordingListeners.add(listener);
  }

  /**
   * 移除录制事件监听器
   */
  removeRecordingEventListener(listener: RecordingEventListener): void {
    this.recordingListeners.delete(listener);
  }

  /**
   * 添加网络事件监听器
   */
  addNetworkEventListener(listener: NetworkEventListener): void {
    this.networkListeners.add(listener);
  }

  /**
   * 移除网络事件监听器
   */
  removeNetworkEventListener(listener: NetworkEventListener): void {
    this.networkListeners.delete(listener);
  }

  /**
   * 添加文件事件监听器
   */
  addFileEventListener(listener: FileEventListener): void {
    this.fileListeners.add(listener);
  }

  /**
   * 移除文件事件监听器
   */
  removeFileEventListener(listener: FileEventListener): void {
    this.fileListeners.delete(listener);
  }

  /**
   * 添加消息事件监听器
   */
  addMessageEventListener(listener: MessageEventListener): void {
    this.messageListeners.add(listener);
  }

  /**
   * 移除消息事件监听器
   */
  removeMessageEventListener(listener: MessageEventListener): void {
    this.messageListeners.delete(listener);
  }

  /**
   * 注册原生事件监听器
   */
  private registerNativeEventListeners(): void {
    this.eventCallbacks.forEach((callback, eventType) => {
      try {
        this.biShareManager.on(eventType, callback);
        Log.showInfo(TAG, `注册事件监听器: ${EventType[eventType]}`);
      } catch (error) {
        Log.showError(TAG, `注册事件监听器失败: ${EventType[eventType]}`, error);
      }
    });
  }

  /**
   * 注销原生事件监听器
   */
  private unregisterNativeEventListeners(): void {
    this.eventCallbacks.forEach((callback, eventType) => {
      try {
        this.biShareManager.off(eventType, callback);
        Log.showInfo(TAG, `注销事件监听器: ${EventType[eventType]}`);
      } catch (error) {
        Log.showError(TAG, `注销事件监听器失败: ${EventType[eventType]}`, error);
      }
    });
  }

  /**
   * 处理设备信息事件
   */
  private handleDeviceInfoEvent(data: NativeEventData): void {
    try {
      Log.showInfo(TAG, '收到设备信息事件');
      const deviceInfo = JSON.parse(data.data) as Record<string, Object>;

      // 转换为扩展设备信息
      const extendedDevice: ExtendedDeviceInfo = {
        id: (deviceInfo.id as string) || '',
        name: (deviceInfo.name as string) || '',
        address: (deviceInfo.address as string) || '',
        model: (deviceInfo.model as string) || '',
        pincode: (deviceInfo.pincode as string) || '',
        status: (deviceInfo.status as number) || 0,
        connected: (deviceInfo.connected as boolean) || false,
        connectionStatus: (deviceInfo.connected as boolean) ?
          this.getConnectionStatusFromNative(deviceInfo.status as number) :
          this.getConnectionStatusFromNative(0),
        connectionCount: 0
      };

      // 通知设备监听器
      this.notifyDeviceDiscovered(extendedDevice);
    } catch (error) {
      Log.showError(TAG, '处理设备信息事件失败:', error);
    }
  }

  /**
   * 处理设备列表事件
   */
  private handleDeviceListEvent(data: NativeEventData): void {
    try {
      Log.showInfo(TAG, '收到设备列表事件');
      const deviceList = JSON.parse(data.data) as Record<string, Object>[];

      if (Array.isArray(deviceList)) {
        for (const deviceInfo of deviceList) {
          const extendedDevice: ExtendedDeviceInfo = {
            id: (deviceInfo.id as string) || '',
            name: (deviceInfo.name as string) || '',
            address: (deviceInfo.address as string) || '',
            model: (deviceInfo.model as string) || '',
            pincode: (deviceInfo.pincode as string) || '',
            status: (deviceInfo.status as number) || 0,
            connected: (deviceInfo.connected as boolean) || false,
            connectionStatus: this.getConnectionStatusFromNative((deviceInfo.status as number) || 0),
            connectionCount: 0
          };

          this.notifyDeviceDiscovered(extendedDevice);
        }
      }
    } catch (error) {
      Log.showError(TAG, '处理设备列表事件失败:', error);
    }
  }

  /**
   * 处理连接状态事件
   */
  private handleConnectionStatusEvent(data: NativeEventData): void {
    try {
      Log.showInfo(TAG, '收到连接状态事件');
      const statusInfo = JSON.parse(data.data) as Record<string, Object>;

      // 根据状态信息通知相应的监听器
      if (statusInfo.connected as boolean) {
        // 设备连接事件
        this.notifyDeviceConnected(statusInfo);
      } else {
        // 设备断开事件
        this.notifyDeviceDisconnected(statusInfo);
      }
    } catch (error) {
      Log.showError(TAG, '处理连接状态事件失败:', error);
    }
  }

  /**
   * 处理录制事件
   */
  private handleRecordingEvent(data: NativeEventData): void {
    try {
      Log.showInfo(TAG, '收到录制事件');
      const recordingInfo = JSON.parse(data.data) as Record<string, Object>;

      // 根据录制状态通知监听器
      switch (recordingInfo.action as string) {
        case 'start':
          this.notifyRecordingStarted(recordingInfo);
          break;
        case 'stop':
          this.notifyRecordingStopped(recordingInfo);
          break;
        case 'pause':
          this.notifyRecordingPaused(recordingInfo);
          break;
        case 'resume':
          this.notifyRecordingResumed(recordingInfo);
          break;
      }
    } catch (error) {
      Log.showError(TAG, '处理录制事件失败:', error);
    }
  }

  /**
   * 从原生状态转换为连接状态
   */
  private getConnectionStatusFromNative(nativeStatus: number): DeviceConnectionStatus {
    switch (nativeStatus) {
      case 0:
        return DeviceConnectionStatus.DISCONNECTED;
      case 1:
        return DeviceConnectionStatus.CONNECTING;
      case 2:
        return DeviceConnectionStatus.CONNECTED;
      default:
        return DeviceConnectionStatus.FAILED;
    }
  }

  /**
   * 通知设备发现
   */
  private notifyDeviceDiscovered(device: ExtendedDeviceInfo): void {
    this.deviceListeners.forEach(listener => {
      try {
        listener.onDeviceDiscovered(device);
      } catch (error) {
        Log.showError(TAG, '设备发现事件通知失败:', error);
      }
    });
  }

  /**
   * 通知设备连接
   */
  private notifyDeviceConnected(deviceInfo: Record<string, Object>): void {
    const device: ExtendedDeviceInfo = {
      id: deviceInfo.id as string || '',
      name: deviceInfo.name as string || '',
      address: deviceInfo.address as string || '',
      model: deviceInfo.model as string || '',
      pincode: deviceInfo.pincode as string || '',
      status: deviceInfo.status as number || 0,
      connected: true,
      connectionStatus: DeviceConnectionStatus.CONNECTED,
      connectionCount: (deviceInfo.connectionCount as number || 0) + 1
    };

    this.deviceListeners.forEach(listener => {
      try {
        listener.onDeviceConnected(device);
      } catch (error) {
        Log.showError(TAG, '设备连接事件通知失败:', error);
      }
    });
  }

  /**
   * 通知设备断开连接
   */
  private notifyDeviceDisconnected(deviceInfo: Record<string, Object>): void {
    const device: ExtendedDeviceInfo = {
      id: deviceInfo.id as string || '',
      name: deviceInfo.name as string || '',
      address: deviceInfo.address as string || '',
      model: deviceInfo.model as string || '',
      pincode: deviceInfo.pincode as string || '',
      status: deviceInfo.status as number || 0,
      connected: false,
      connectionStatus: DeviceConnectionStatus.DISCONNECTED,
      connectionCount: deviceInfo.connectionCount as number || 0
    };

    this.deviceListeners.forEach(listener => {
      try {
        listener.onDeviceDisconnected(device);
      } catch (error) {
        Log.showError(TAG, '设备断开连接事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制开始
   */
  private notifyRecordingStarted(recordingInfo: Record<string, Object>): void {
    const info: RecordingInfo = {
      status: RecordingStatus.RECORDING,
      filePath: recordingInfo.filePath as string,
      startTime: Date.now(),
      duration: 0
    };

    this.recordingListeners.forEach(listener => {
      try {
        listener.onRecordingStarted(info);
      } catch (error) {
        Log.showError(TAG, '录制开始事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制停止
   */
  private notifyRecordingStopped(recordingInfo: Record<string, Object>): void {
    const info: RecordingInfo = {
      status: RecordingStatus.STOPPED,
      filePath: recordingInfo.filePath as string,
      duration: recordingInfo.duration as number || 0
    };

    this.recordingListeners.forEach(listener => {
      try {
        listener.onRecordingStopped(info);
      } catch (error) {
        Log.showError(TAG, '录制停止事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制暂停
   */
  private notifyRecordingPaused(recordingInfo: Record<string, Object>): void {
    const info: RecordingInfo = {
      status: RecordingStatus.PAUSED,
      filePath: recordingInfo.filePath as string
    };

    this.recordingListeners.forEach(listener => {
      try {
        listener.onRecordingPaused(info);
      } catch (error) {
        Log.showError(TAG, '录制暂停事件通知失败:', error);
      }
    });
  }

  /**
   * 通知录制恢复
   */
  private notifyRecordingResumed(recordingInfo: Record<string, Object>): void {
    const info: RecordingInfo = {
      status: RecordingStatus.RECORDING,
      filePath: recordingInfo.filePath as string
    };

    this.recordingListeners.forEach(listener => {
      try {
        listener.onRecordingResumed(info);
      } catch (error) {
        Log.showError(TAG, '录制恢复事件通知失败:', error);
      }
    });
  }

  /**
   * 手动触发设备发现事件（用于测试）
   */
  simulateDeviceDiscovered(deviceId: string, deviceName: string): void {
    const mockDevice: ExtendedDeviceInfo = {
      id: deviceId,
      name: deviceName,
      address: '*************',
      model: 'Test Device',
      pincode: '1234',
      status: 0,
      connected: false,
      connectionStatus: DeviceConnectionStatus.DISCONNECTED,
      connectionCount: 0
    };

    Log.showInfo(TAG, `模拟设备发现: ${deviceId} - ${deviceName}`);
    this.notifyDeviceDiscovered(mockDevice);
  }
}
