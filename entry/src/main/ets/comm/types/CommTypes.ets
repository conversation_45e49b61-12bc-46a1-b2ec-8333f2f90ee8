/**
 * Entry模块通信层类型定义
 */

/**
 * UI适配器配置选项
 */
export interface UIAdapterConfig {
  /**
   * 是否启用自动刷新
   */
  autoRefresh?: boolean;
  
  /**
   * 刷新间隔（毫秒）
   */
  refreshInterval?: number;
  
  /**
   * 最大重试次数
   */
  maxRetries?: number;
  
  /**
   * 超时时间（毫秒）
   */
  timeout?: number;
}

/**
 * 服务状态
 */
export enum ServiceStatus {
  UNINITIALIZED = 'uninitialized',
  INITIALIZING = 'initializing',
  INITIALIZED = 'initialized',
  RELEASING = 'releasing',
  ERROR = 'error'
}

/**
 * 操作结果状态
 */
export enum OperationStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending',
  CANCELLED = 'cancelled'
}

/**
 * 管理器基础接口
 */
export interface IManager {
  /**
   * 初始化管理器
   */
  initialize(): Promise<void>;
  
  /**
   * 释放管理器
   */
  release(): Promise<void>;
  
  /**
   * 获取管理器状态
   */
  getStatus(): ServiceStatus;
}

/**
 * 事件监听器基础接口
 */
export interface IEventListener {
  /**
   * 监听器ID
   */
  readonly id: string;
  
  /**
   * 是否激活
   */
  isActive(): boolean;
  
  /**
   * 激活监听器
   */
  activate(): void;
  
  /**
   * 停用监听器
   */
  deactivate(): void;
}

/**
 * 适配器状态信息
 */
export interface AdapterStatus {
  /**
   * 是否已初始化
   */
  isInitialized: boolean;
  
  /**
   * 发现的设备数量
   */
  discoveredDeviceCount: number;
  
  /**
   * 连接的设备数量
   */
  connectedDeviceCount: number;
  
  /**
   * 是否正在发现设备
   */
  isDiscovering: boolean;
  
  /**
   * 是否正在录制
   */
  isRecording: boolean;
  
  /**
   * 网络连接状态
   */
  networkConnected: boolean;
  
  /**
   * 最后更新时间
   */
  lastUpdateTime: number;
}

/**
 * 操作上下文
 */
export interface OperationContext {
  /**
   * 操作ID
   */
  operationId: string;
  
  /**
   * 操作类型
   */
  operationType: string;
  
  /**
   * 开始时间
   */
  startTime: number;
  
  /**
   * 超时时间
   */
  timeout?: number;
  
  /**
   * 重试次数
   */
  retryCount?: number;
  
  /**
   * 额外参数
   */
  params?: Record<string, any>;
}

/**
 * 批量操作结果
 */
export interface BatchOperationResult<T> {
  /**
   * 成功的结果
   */
  successes: T[];
  
  /**
   * 失败的结果
   */
  failures: Array<{
    item: any;
    error: Error;
  }>;
  
  /**
   * 总数
   */
  total: number;
  
  /**
   * 成功数
   */
  successCount: number;
  
  /**
   * 失败数
   */
  failureCount: number;
}
