/**
 * Entry模块通信层统一导出
 */

// 适配器
export { UIAdapter } from './adapters/UIAdapter';

// 服务
export { BiShareService } from './service/BiShareService';

// 管理器
export { DeviceManager } from './managers/DeviceManager';
export { NetworkManager } from './managers/NetworkManager';
export { RecordingManager } from './managers/RecordingManager';
export { EventManager } from './managers/EventManager';

// 类型定义
export * from './types/CommTypes';

// 常量
export * from './constants/CommConstants';

// 工具类
export { NetworkUtils } from './utils/NetworkUtils';
