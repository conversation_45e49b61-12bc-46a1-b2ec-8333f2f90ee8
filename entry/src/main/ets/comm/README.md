# Entry模块通信层 (comm)

## 目录结构

```
comm/
├── adapters/           # 适配器层
│   └── UIAdapter.ets   # UI适配器，为UI层提供简化接口
├── managers/           # 管理器层
│   ├── DeviceManager.ets    # 设备管理器
│   ├── NetworkManager.ets   # 网络管理器
│   ├── RecordingManager.ets # 录制管理器
│   └── EventManager.ets     # 事件管理器
├── service/            # 服务层
│   └── BiShareService.ets   # BiShare统一服务
├── types/              # 类型定义
│   └── CommTypes.ets   # 通信层类型定义
├── constants/          # 常量定义
│   └── CommConstants.ets    # 通信层常量
├── utils/              # 工具类
│   └── NetworkUtils.ets     # 网络工具类
├── index.ets           # 统一导出文件
└── README.md           # 说明文档
```

## 架构设计

### 分层架构

1. **UI层** (pages/components)
   - 负责用户界面展示
   - 通过UIAdapter与业务层交互

2. **适配器层** (adapters)
   - UIAdapter: 为UI层提供简化的API接口
   - 负责数据转换和状态管理
   - 提供响应式数据绑定支持

3. **服务层** (service)
   - BiShareService: 统一的服务管理入口
   - 负责各功能管理器的协调
   - 统一的错误处理和状态管理

4. **管理器层** (managers)
   - DeviceManager: 设备发现、连接、管理
   - NetworkManager: 网络连接、状态监控
   - RecordingManager: 录制功能管理
   - EventManager: 事件监听和分发

5. **底层API** (bishare模块)
   - 提供原生功能接口
   - NAPI层实现

### 设计原则

- **单一职责**: 每个类只负责一个特定功能
- **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
- **开闭原则**: 对扩展开放，对修改关闭
- **接口隔离**: 使用小而专的接口

## 使用方式

### 1. 基本使用

```typescript
import { UIAdapter } from '../comm';

// 获取适配器实例
const adapter = UIAdapter.getInstance();

// 初始化服务
const success = await adapter.initialize(context, options);

// 开始设备发现
await adapter.startDeviceDiscovery();

// 获取发现的设备
const devices = adapter.getDiscoveredDevices();
```

### 2. 网络管理

```typescript
import { NetworkUtils } from '../comm';

// 获取当前网络信息
const networkInfo = await NetworkUtils.getCurrentNetworkInfo();

// 验证网络信息
const isValid = NetworkUtils.validateNetworkInfo(networkInfo);

// 检查网络连接
const isConnected = await NetworkUtils.isNetworkConnected();
```

### 3. 事件监听

```typescript
import { UIAdapter, DeviceEventListener } from '../comm';

const adapter = UIAdapter.getInstance();

// 创建设备事件监听器
class MyDeviceListener extends DeviceEventListener {
  onDeviceDiscovered(device: ExtendedDeviceInfo): void {
    console.log('发现设备:', device.id);
  }
  
  onDeviceConnected(device: ExtendedDeviceInfo): void {
    console.log('设备已连接:', device.id);
  }
}

// 注册监听器
const eventManager = adapter.getEventManager();
eventManager.addDeviceEventListener(new MyDeviceListener());
```

## 配置选项

### UIAdapter配置

```typescript
interface UIAdapterConfig {
  autoRefresh?: boolean;      // 是否启用自动刷新
  refreshInterval?: number;   // 刷新间隔（毫秒）
  maxRetries?: number;        // 最大重试次数
  timeout?: number;           // 超时时间（毫秒）
}
```

### 默认配置

- 默认超时时间: 30秒
- 默认重试次数: 3次
- 默认刷新间隔: 5秒
- 设备发现超时: 60秒
- 设备连接超时: 15秒

## 错误处理

所有异步操作都返回 `BiShareResult<T>` 类型：

```typescript
interface BiShareResult<T> {
  success: boolean;
  data?: T;
  error?: BiShareError;
}
```

错误代码定义在 `ErrorCodes` 类中：

- `SERVICE_NOT_INITIALIZED`: 服务未初始化
- `DEVICE_DISCOVERY_FAILED`: 设备发现失败
- `NETWORK_SETUP_FAILED`: 网络设置失败
- 等等...

## 扩展指南

### 添加新的管理器

1. 在 `managers/` 目录下创建新的管理器类
2. 实现 `IManager` 接口
3. 在 `BiShareService` 中注册新管理器
4. 在 `index.ets` 中导出新管理器

### 添加新的事件类型

1. 在 `types/CommTypes.ets` 中定义新的事件类型
2. 在 `EventManager` 中添加相应的监听器支持
3. 在相关管理器中触发新事件

### 添加新的工具类

1. 在 `utils/` 目录下创建新的工具类
2. 遵循静态方法设计模式
3. 在 `index.ets` 中导出新工具类

## 注意事项

1. **线程安全**: 所有管理器都是单例模式，注意线程安全
2. **内存管理**: 及时释放资源，避免内存泄漏
3. **错误处理**: 所有异步操作都要进行错误处理
4. **日志记录**: 使用统一的日志标签，便于调试
5. **权限检查**: 网络相关操作需要检查相应权限

## TODO

- [ ] 集成OpenHarmony真实的网络API
- [ ] 实现真实的设备发现机制
- [ ] 添加更多的网络状态监控
- [ ] 优化错误处理机制
- [ ] 添加单元测试
- [ ] 性能优化和内存管理
