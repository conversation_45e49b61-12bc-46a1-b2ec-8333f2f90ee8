/**
 * Entry模块通信层常量定义
 */

/**
 * 默认配置常量
 */
export class DefaultConfig {
  /**
   * 默认超时时间（毫秒）
   */
  static readonly DEFAULT_TIMEOUT = 30000;
  
  /**
   * 默认重试次数
   */
  static readonly DEFAULT_MAX_RETRIES = 3;
  
  /**
   * 默认刷新间隔（毫秒）
   */
  static readonly DEFAULT_REFRESH_INTERVAL = 5000;
  
  /**
   * 设备发现超时时间（毫秒）
   */
  static readonly DEVICE_DISCOVERY_TIMEOUT = 60000;
  
  /**
   * 设备连接超时时间（毫秒）
   */
  static readonly DEVICE_CONNECTION_TIMEOUT = 15000;
  
  /**
   * 网络设置超时时间（毫秒）
   */
  static readonly NETWORK_SETUP_TIMEOUT = 10000;
}

/**
 * 错误代码常量
 */
export class ErrorCodes {
  /**
   * 服务未初始化
   */
  static readonly SERVICE_NOT_INITIALIZED = 'SERVICE_NOT_INITIALIZED';
  
  /**
   * 服务初始化失败
   */
  static readonly SERVICE_INIT_FAILED = 'SERVICE_INIT_FAILED';
  
  /**
   * 服务释放失败
   */
  static readonly SERVICE_RELEASE_FAILED = 'SERVICE_RELEASE_FAILED';
  
  /**
   * 设备发现失败
   */
  static readonly DEVICE_DISCOVERY_FAILED = 'DEVICE_DISCOVERY_FAILED';
  
  /**
   * 设备连接失败
   */
  static readonly DEVICE_CONNECTION_FAILED = 'DEVICE_CONNECTION_FAILED';
  
  /**
   * 网络设置失败
   */
  static readonly NETWORK_SETUP_FAILED = 'NETWORK_SETUP_FAILED';
  
  /**
   * 参数无效
   */
  static readonly INVALID_PARAMETER = 'INVALID_PARAMETER';
  
  /**
   * 操作超时
   */
  static readonly OPERATION_TIMEOUT = 'OPERATION_TIMEOUT';
  
  /**
   * 操作取消
   */
  static readonly OPERATION_CANCELLED = 'OPERATION_CANCELLED';
  
  /**
   * 权限不足
   */
  static readonly PERMISSION_DENIED = 'PERMISSION_DENIED';
}

/**
 * 事件类型常量
 */
export class EventTypes {
  /**
   * 适配器状态变化
   */
  static readonly ADAPTER_STATUS_CHANGED = 'adapter_status_changed';
  
  /**
   * 服务状态变化
   */
  static readonly SERVICE_STATUS_CHANGED = 'service_status_changed';
  
  /**
   * 管理器状态变化
   */
  static readonly MANAGER_STATUS_CHANGED = 'manager_status_changed';
  
  /**
   * 操作开始
   */
  static readonly OPERATION_STARTED = 'operation_started';
  
  /**
   * 操作完成
   */
  static readonly OPERATION_COMPLETED = 'operation_completed';
  
  /**
   * 操作失败
   */
  static readonly OPERATION_FAILED = 'operation_failed';
}

/**
 * 日志标签常量
 */
export class LogTags {
  /**
   * UI适配器
   */
  static readonly UI_ADAPTER = 'UIAdapter';
  
  /**
   * BiShare服务
   */
  static readonly BISHARE_SERVICE = 'BiShareService';
  
  /**
   * 设备管理器
   */
  static readonly DEVICE_MANAGER = 'DeviceManager';
  
  /**
   * 网络管理器
   */
  static readonly NETWORK_MANAGER = 'NetworkManager';
  
  /**
   * 录制管理器
   */
  static readonly RECORDING_MANAGER = 'RecordingManager';
  
  /**
   * 事件管理器
   */
  static readonly EVENT_MANAGER = 'EventManager';
}

/**
 * 网络相关常量
 */
export class NetworkConstants {
  /**
   * 默认网络类型
   */
  static readonly DEFAULT_NETWORK_TYPE = 1; // WLAN
  
  /**
   * IP地址正则表达式
   */
  static readonly IP_ADDRESS_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  /**
   * MAC地址正则表达式
   */
  static readonly MAC_ADDRESS_REGEX = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
  
  /**
   * 默认端口范围
   */
  static readonly DEFAULT_PORT_RANGE = {
    MIN: 8000,
    MAX: 9000
  };
}

/**
 * 设备相关常量
 */
export class DeviceConstants {
  /**
   * 最大设备数量
   */
  static readonly MAX_DEVICE_COUNT = 50;
  
  /**
   * 设备ID最大长度
   */
  static readonly MAX_DEVICE_ID_LENGTH = 64;
  
  /**
   * 设备名称最大长度
   */
  static readonly MAX_DEVICE_NAME_LENGTH = 128;
  
  /**
   * 默认设备类型
   */
  static readonly DEFAULT_DEVICE_TYPE = 'unknown';
}
