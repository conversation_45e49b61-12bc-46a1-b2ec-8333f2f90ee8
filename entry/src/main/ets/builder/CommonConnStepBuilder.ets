
/**
 * 公共的连接步骤Builder
 */
@Builder
export function CommonConnStepBuilder(firstText: ResourceStr, secondText: ResourceStr, thirdText: ResourceStr) {
  Column() {
    Row() {
      Stack() {
        Circle()
          .width($r('app.float.vp_32'))
          .height($r('app.float.vp_32'))
          .fill($r('app.color.color_EBEDF0'))
        Text($r('app.string.one'))
          .fontSize($r('app.float.fp_14'))
          .fontColor($r('app.color.color_606266'))
      }.margin({
        left: $r('app.float.vp_106'),
        right: $r('app.float.vp_34')
      })
      Divider()
        .vertical(false)
        .color($r('app.color.color_DCDFE6'))
        .width($r('app.float.vp_186'))
        .height($r('app.float.vp_1'))
        .margin({
          right: $r('app.float.vp_34')
        })

      Stack() {
        Circle()
          .width($r('app.float.vp_32'))
          .height($r('app.float.vp_32'))
          .fill($r('app.color.color_EBEDF0'))
        Text($r('app.string.two'))
          .fontSize($r('app.float.fp_14'))
          .fontColor($r('app.color.color_606266'))
      }.margin({
        right: $r('app.float.vp_34')
      })
      Divider()
        .vertical(false)
        .color($r('app.color.color_DCDFE6'))
        .width($r('app.float.vp_186'))
        .height($r('app.float.vp_1'))
        .margin({
          right: $r('app.float.vp_34')
        })

      Stack() {
        Circle()
          .width($r('app.float.vp_32'))
          .height($r('app.float.vp_32'))
          .fill($r('app.color.color_EBEDF0'))
        Text($r('app.string.three'))
          .fontSize($r('app.float.fp_14'))
          .fontColor($r('app.color.color_606266'))
      }.margin({
        right: $r('app.float.vp_106')
      })
    }.margin({
      bottom: $r('app.float.vp_12')
    })

    Row() {
      Text(firstText)
        .fontSize($r('app.float.fp_20'))
        .fontColor($r('app.color.color_303133'))
        .fontWeight(FontWeight.Bold)
        .margin({
          left: $r('app.float.vp_79'),
          right: $r('app.float.vp_200')
        })
      Text(secondText)
        .fontSize($r('app.float.fp_20'))
        .fontColor($r('app.color.color_303133'))
        .fontWeight(FontWeight.Bold)
        .margin({
          right: $r('app.float.vp_200')
        })
      Text(thirdText)
        .fontSize($r('app.float.fp_20'))
        .fontColor($r('app.color.color_303133'))
        .margin({
          right: $r('app.float.vp_79')
        })
    }.margin({
      bottom: $r('app.float.vp_8')
    })
  }
}