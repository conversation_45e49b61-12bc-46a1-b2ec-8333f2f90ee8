import { SettingItemType, SettingListItemBean } from '../../bean/SettingListItemBean'
import { CastWaterSettingComponent } from '../../componnets/setting/CastWaterSettingComponent';


@Component
export struct CustomSettingDialogComponent {

    @Provide('settingStack') settingStack: NavPathStack = new NavPathStack();

    private settingItemList: SettingListItemBean[] =
        [new SettingListItemBean(SettingItemType.TYPE_1)
        , new SettingListItemBean(SettingItemType.TYPE_2)
        , new SettingListItemBean(SettingItemType.TYPE_3)
        , new SettingListItemBean(SettingItemType.TYPE_4)]

    @State castWater: string = '设备名+日期'

    @State deviceName: string = 'BWB86-GI4C3'

    @State versionName: string = '3.0.20240408'

    build() {
        Navigation(this.settingStack) {
            Column() {
                //设置 - 帮助
                this.SettingHelpBuilder()

                List({ space: '8vp', initialIndex: 0 }) {
                    ForEach(this.settingItemList, (item: SettingListItemBean) => {
                        ListItem() {
                            if (item.type == SettingItemType.TYPE_1) {
                                this.castCodeAndWaterBuilder()
                            } else if (item.type == SettingItemType.TYPE_2) {
                                this.deviceNameAndShowIpBuilder()
                            } else if (item.type == SettingItemType.TYPE_3) {
                                this.securityAndStretchingBuilder()
                            } else {
                                //默认是检查版本
                                this.checkVersionBuilder()
                            }
                        }
                    }, (item: SettingListItemBean) => item.type.toString())
                }
                .scrollBar(BarState.Auto)
                .margin({bottom: $r('app.float.vp_28')})
            }
            .margin({
                left: $r('app.float.vp_16'),
                right: $r('app.float.vp_16'),
                top: $r('app.float.vp_16'),
                bottom: $r('app.float.vp_28')
            })
        }
        .hideTitleBar(true)
        .hideToolBar(true)
        .hideNavBar(true)
        .navDestination(this.PageMap)
        .width($r('app.float.vp_424'))
        .height($r('app.float.vp_566'))
        .borderRadius($r('app.float.vp_16'))
        .backgroundColor($r('app.color.color_EBEDF0'))
    }

    @Builder
    PageMap(name: string) {
        if (name === 'watermark') {
            CastWaterSettingComponent()
        } else if (name === 'pageTwo') {
            // pageTwoTmp({ names: name, values: this.pageInfos } as Pages)
        }
    }

    /**
     * 设置 - 帮助
     */
    @Builder
    SettingHelpBuilder() {
        Row() {
            Text($r('app.string.setting'))
                .fontSize($r('app.float.vp_24'))
                .fontColor($r('app.color.color_303133'))
                .fontWeight(FontWeight.Bold)

            Row() {
                Image($r('app.media.ic_help'))
                    .width($r('app.float.vp_20'))
                    .height($r('app.float.vp_20'))
                Text($r('app.string.help'))
                    .fontSize($r('app.float.vp_16'))
                    .fontColor($r('app.color.color_606266'))
                    .margin({left: $r('app.float.vp_1')})
            }.onClick(() => {
                //todo 待实现“帮助”点击
            })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({
            bottom: $r('app.float.vp_8'),
        })
    }

    /**
     * 投屏码显示、投屏水印
     */
    @Builder
    castCodeAndWaterBuilder() {
        Column() {
            //投屏码显示
            this.castCodeShowBuilder()
            Divider().width('100%').height($r('app.float.vp_1'))
            //投屏水印
            this.castWaterMarkBuilder()
        }.listItemStyle()
    }

    /**
     * 投屏码显示
     */
    @Builder
    castCodeShowBuilder() {
        Row() {
            Text($r('app.string.cast_code_show'))
                .fontColor($r('app.color.color_303133'))
                .fontSize($r('app.float.vp_20'))

            Toggle({ type: ToggleType.Switch, isOn: false })
                .selectedColor($r('app.color.color_0081FF'))
                .switchPointColor($r('app.color.color_99A5B3'))
                .onChange((isOn: boolean) => {
                    console.info('Component cast status:' + isOn)
                })
                .width($r('app.float.vp_45'))
                .height($r('app.float.vp_24'))
        }.width('100%')
        .height($r('app.float.vp_76'))
        .justifyContent(FlexAlign.SpaceBetween)
    }

    /**
     * 投屏水印
     */
    @Builder
    castWaterMarkBuilder() {
        Row() {
            Text($r('app.string.cast_water'))
                .fontColor($r('app.color.color_303133'))
                .fontSize($r('app.float.vp_20'))

            Row() {
                Text(this.castWater)
                    .fontColor($r('app.color.color_A8ABB3'))
                    .margin({right: $r('app.float.vp_8')})
                    .fontSize($r('app.float.vp_20'))

                Image($r("app.media.ic_forward_grey"))
                    .width($r('app.float.vp_24'))
                    .height($r('app.float.vp_24'))
            }
        }.width('100%')
        .height($r('app.float.vp_76'))
        .justifyContent(FlexAlign.SpaceBetween)
        .onClick(() => {
            this.settingStack.pushPath({ name: 'watermark' }) //将name指定的NavDestination页面信息入栈
        })
    }

    /**
     * 设备名称、显示IP
     */
    @Builder
    deviceNameAndShowIpBuilder() {
        Column() {
            //设备名称
            this.deviceNameBuilder()
            Divider().width('100%').height($r('app.float.vp_1'))
            //显示IP
            this.showIPBuilder()
        }.listItemStyle()
    }

    /**
     * 设备名称
     */
    @Builder
    deviceNameBuilder() {
        Row() {
            Text($r('app.string.device_name'))
                .fontColor($r('app.color.color_303133'))
                .fontSize($r('app.float.vp_20'))

            Row() {
                Text(this.deviceName)
                    .fontColor($r('app.color.color_A8ABB3'))
                    .margin({right: $r('app.float.vp_8')})
                    .fontSize($r('app.float.vp_20'))

                Image($r("app.media.ic_forward_grey"))
                    .width($r('app.float.vp_24'))
                    .height($r('app.float.vp_24'))
            }
        }.width('100%')
        .height($r('app.float.vp_76'))
        .justifyContent(FlexAlign.SpaceBetween)
        .onClick(() => {
            //todo 待实现“设备名称”点击
        })
    }

    /**
     * 显示IP
     */
    @Builder
    showIPBuilder() {
        Row() {
            Text($r('app.string.show_ip'))
                .fontColor($r('app.color.color_303133'))
                .fontSize($r('app.float.vp_20'))

            Toggle({ type: ToggleType.Switch, isOn: false })
                .selectedColor($r('app.color.color_0081FF'))
                .switchPointColor($r('app.color.color_99A5B3'))
                .onChange((isOn: boolean) => {
                    console.info('Component showIP status:' + isOn)
                })
                .width($r('app.float.vp_45'))
                .height($r('app.float.vp_24'))
        }.width('100%')
        .height($r('app.float.vp_76'))
        .justifyContent(FlexAlign.SpaceBetween)
    }

    /**
     * 安全设置、智能拉伸
     */
    @Builder
    securityAndStretchingBuilder() {
        Column() {
            //安全设置
            this.securitySettingBuilder()
            Divider().width('100%').height($r('app.float.vp_1'))
            //智能拉伸
            this.stretchingBuilder()
        }.listItemStyle()
    }

    /**
     * 安全设置
     */
    @Builder
    securitySettingBuilder() {
        Row() {
            Text($r('app.string.device_name'))
                .fontColor($r('app.color.color_303133'))
                .fontSize($r('app.float.vp_20'))

            Image($r("app.media.ic_forward_grey"))
                .width($r('app.float.vp_24'))
                .height($r('app.float.vp_24'))
        }.width('100%')
        .height($r('app.float.vp_76'))
        .justifyContent(FlexAlign.SpaceBetween)
        .onClick(() => {
            //todo 待实现“安全设置”点击
        })
    }

    /**
     * 智能拉伸
     */
    @Builder
    stretchingBuilder() {
        Row() {
            Text($r('app.string.intelligent_stretching'))
                .fontColor($r('app.color.color_303133'))
                .fontSize($r('app.float.vp_20'))

            Toggle({ type: ToggleType.Switch, isOn: false })
                .selectedColor($r('app.color.color_0081FF'))
                .switchPointColor($r('app.color.color_99A5B3'))
                .onChange((isOn: boolean) => {
                    console.info('Component showIP status:' + isOn)
                })
                .width($r('app.float.vp_45'))
                .height($r('app.float.vp_24'))
        }.width('100%')
        .height($r('app.float.vp_76'))
        .justifyContent(FlexAlign.SpaceBetween)
    }

    /**
     * 检查版本
     */
    @Builder
    checkVersionBuilder() {
        Column() {
            Row() {
                Text($r('app.string.check_update'))
                    .fontColor($r('app.color.color_303133'))
                    .fontSize($r('app.float.vp_20'))

                Row() {
                    Text(this.versionName)
                        .fontColor($r('app.color.color_A8ABB3'))
                        .margin({right: $r('app.float.vp_8')})
                        .fontSize($r('app.float.vp_20'))

                    Image($r("app.media.ic_forward_grey"))
                        .width($r('app.float.vp_24'))
                        .height($r('app.float.vp_24'))
                }
            }.width('100%')
            .height($r('app.float.vp_76'))
            .justifyContent(FlexAlign.SpaceBetween)
        }.width($r('app.float.vp_392'))
        .backgroundColor($r('app.color.color_FAFAFA'))
        .borderRadius($r('app.float.vp_8'))
        .padding({
            left: $r('app.float.vp_16'),
            right: $r('app.float.vp_16')
        })
        .onClick(() => {
            //todo 待实现“检查版本”点击
        })
    }

    @Styles
    listItemStyle() {
        .width($r('app.float.vp_392'))
        .height($r('app.float.vp_153'))
        .backgroundColor($r('app.color.color_FAFAFA'))
        .borderRadius($r('app.float.vp_8'))
        .padding({
            left: $r('app.float.vp_16'),
            right: $r('app.float.vp_16')
        })
    }

}
