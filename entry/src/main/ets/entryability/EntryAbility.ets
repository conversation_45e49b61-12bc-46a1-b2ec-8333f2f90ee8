import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import hilog from '@ohos.hilog';
import UIAbility from '@ohos.app.ability.UIAbility';
import Want from '@ohos.app.ability.Want';
import window from '@ohos.window';
import { BiShareHelper } from '../helper/BiShareHelper';
import { InfoCmdManager, Log } from '@ohos/lib_info_sender';
import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';
import abilityAccessCtrl, { PermissionRequestResult, Permissions } from '@ohos.abilityAccessCtrl';
import bundleManager from '@ohos.bundle.bundleManager';
import common from '@ohos.app.ability.common';
import { BusinessError } from '@ohos.base';


const TAG = 'EntryAbility';

export default class EntryAbility extends UIAbility {
  private isBiShareInitialized: boolean = false;

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onCreate');
  }

  onDestroy(): void {
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onDestroy');
    // 销毁bishare
    BiShareHelper.getInstance().destroy();
    //取消网络监听
    InfoCmdManager.getInstance().unNetworkListen();
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageCreate');
    // 检查权限
    this.checkPermissions();

    windowStage.loadContent('pages/Index', (err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'testTag', 'Failed to load the content. Cause: %{public}s', JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'testTag', 'Succeeded in loading the content. Data: %{public}s', JSON.stringify(data) ?? '');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    // Ability has brought to foreground
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onForeground');

    this.initShareService();
    this.registerNetListener();
  }

  private async initShareService() {
    try {
      // 初始化bishare
      const isInitSuccess = await BiShareHelper.getInstance().initBiShareService(this.context.getApplicationContext())
      Log.showInfo(TAG, `BiShare initialization result: ${isInitSuccess}`);
      this.isBiShareInitialized = isInitSuccess;
    } catch (error) {
      Log.showError(TAG, `Failed to initialize BiShare service: ${error}`);
      this.isBiShareInitialized = false;
    }
  }

  private registerNetListener() {
    //监听网络情况
    InfoCmdManager.getInstance().networkListen((isConnected: boolean) => {
      Log.showInfo(TAG, 'networkListen isConnected: ' + isConnected);
      if (isConnected) {
        const networkInfo: NetworkInfoOptions = {
          networkType: NetworkType.Wlan,
          addr: '*************',
          mac: '702ad701bd2a'
        }
        try {
          if (this.isBiShareInitialized) {
            // 使用新的UIAdapter设置网络信息
            // 注意：这里需要根据实际需求调整网络信息设置方式
            Log.showInfo(TAG, 'Network connected, BiShare is initialized.');
          } else {
            Log.showWarn(TAG, 'Network connected but BiShare is not initialized yet. Skipping setNetworkInfo.');
          }
        } catch (error) {
          Log.showError(TAG, `Failed to set network info: ${error}. This might happen if BiShare is not initialized yet.`);
        }
      } else {

      }
    });
  }

  onBackground(): void {
    // Ability has back to background
    hilog.info(0x0000, 'testTag', '%{public}s', 'Ability onBackground');
  }

  /**
   * 判断是否拥有权限
   */
  async checkAccessToken(permission: Permissions): Promise<abilityAccessCtrl.GrantStatus> {
    let atManager = abilityAccessCtrl.createAtManager();
    let grantStatus: abilityAccessCtrl.GrantStatus = abilityAccessCtrl.GrantStatus.PERMISSION_DENIED;
    // 获取应用程序的accessTokenID
    let tokenId: number = -1;
    try {
      let bundleInfo: bundleManager.BundleInfo = await bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      let appInfo: bundleManager.ApplicationInfo = bundleInfo.appInfo;
      tokenId = appInfo.accessTokenId;
    } catch (err) {
      Log.showError(TAG, `checkAccessToken getBundleInfoForSelf failed, ${JSON.stringify(err)}`);
    }
    // 校验应用是否被授予权限
    try {
      grantStatus = await atManager.checkAccessToken(tokenId, permission);
    } catch (err) {
      Log.showError(TAG, `checkAccessToken checkAccessToken failed, ${JSON.stringify(err)}`);
    }
    return grantStatus;
  }
  /**
   * 检查哪些权限有
   * @returns
   */
  async checkPermissions(): Promise<void> {
    const permissions: Array<Permissions> = [
      'ohos.permission.GET_NETWORK_INFO',// 网络信息
      'ohos.permission.INTERNET',
      'ohos.permission.GET_WIFI_INFO', // mac地址
      'ohos.permission.READ_DOCUMENT', // 读写本地文件权限
      'ohos.permission.WRITE_DOCUMENT',
    ];
    let havePermissionDeny = false;
    for (let index = 0; index < permissions.length; index++) {
      const perm = permissions[index];
      let grantStatus: abilityAccessCtrl.GrantStatus = await this.checkAccessToken(perm);
      // Log.showInfo(TAG, `checkPermissions checkAccessToken grantStatus ${grantStatus}`);
      if (abilityAccessCtrl.GrantStatus.PERMISSION_DENIED == grantStatus){
        havePermissionDeny = true;
      }
    }
    if (havePermissionDeny) {
      Log.showInfo(TAG, `checkPermissions havePermissionDeny`);
      // 申请网络权限
      this.reqPermissionsFromUser(permissions);
    } else {
      // 已经授权，可以继续访问目标操作
      Log.showInfo(TAG, `checkPermissions all PERMISSION_GRANTED`);
    }
  }
  /**
   * 申请权限
   * @param permissions
   */
  private reqPermissionsFromUser(permissions: Array<Permissions>): void {
    let context = getContext(this) as common.UIAbilityContext;
    let atManager = abilityAccessCtrl.createAtManager();
    Log.showInfo(TAG, `reqPermissionsFromUser permissions ${permissions.length}`);
    // requestPermissionsFromUser会判断权限的授权状态来决定是否唤起弹窗
    atManager.requestPermissionsFromUser(context, permissions, (err: BusinessError, result: PermissionRequestResult) => {
      if (err){
        Log.showError(TAG, `requestPermissionsFromUser failed, ${JSON.stringify(err)}`);
        return;
      }
      // 授权成功
      let grantStatus: Array<number> = result.authResults;
      Log.showInfo(TAG, `reqPermissionsFromUser grantStatus ${JSON.stringify(result)}`);
      let length: number = grantStatus.length;
      for (let i = 0; i < length; i++) {
        if (grantStatus[i] === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
          // 用户授权，可以继续访问目标操作
          Log.showInfo(TAG, `reqPermissionsFromUser permissions[i]: ${permissions[i]} PERMISSION_GRANTED}`);
        } else {
          // 用户拒绝授权，提示用户必须授权才能访问当前页面的功能，并引导用户到系统设置中打开相应的权限
          Log.showError(TAG, `reqPermissionsFromUser permissions[i]: ${permissions[i]} ${grantStatus[i]}}`);
        }
      }
    });
  }

}
