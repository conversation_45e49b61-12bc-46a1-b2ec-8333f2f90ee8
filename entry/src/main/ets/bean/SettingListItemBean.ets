

export enum SettingItemType {
  /**
   * 投屏码显示、投屏水印
   */
  TYPE_1,
  /**
   * 设备名称、显示IP
   */
  TYPE_2,
  /**
   * 安全设置、智能拉伸
   */
  TYPE_3,
  /**
   * 检查版本
   */
  TYPE_4,
}

export class SettingListItemBean {

  /**
   * 点击底部“设置”按钮弹出来的item类型，这里为了方便，以栏来表示(第一栏、第二栏...)，顺序有调整时需要做修改。
   */
  type: SettingItemType = SettingItemType.TYPE_1

  constructor(type: SettingItemType) {
    this.type = type
  }

}