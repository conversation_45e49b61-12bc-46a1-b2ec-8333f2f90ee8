import { BlogPriority, InitOptions } from '@ohos/libbishare_napi';
import common from '@ohos.app.ability.common';
import { Log } from '@ohos/lib_info_sender';
import { UIAdapter } from '../comm/adapters/UIAdapter';
import { DefaultConfig } from '../comm/constants/CommConstants';


const TAG = 'BiShareHelper';
/**
 * BiShare工具类
 */
export class BiShareHelper {

  private static sInstance: BiShareHelper;

  private constructor() {
  }

  public static getInstance() {
    if (!BiShareHelper.sInstance) {
      BiShareHelper.sInstance = new BiShareHelper();
    }
    return BiShareHelper.sInstance;
  }

  public async initBiShareService(context: common.ApplicationContext): Promise<boolean> {
    const initOptions: InitOptions = {
      isConsole: true,
      isFile: true,
      filePath: context.filesDir + "/" + "service_" + Date.parse(new Date().toString()) + ".log",
      priority: BlogPriority.INFO
    };

    try {
      Log.showInfo(TAG, '开始初始化BiShare服务...');

      const uiAdapter = UIAdapter.getInstance();
      const isInitSuccess = await uiAdapter.initialize(context, initOptions);

      if (isInitSuccess) {
        Log.showInfo(TAG, 'BiShare服务初始化成功');

        // 可以在这里添加初始化后的额外配置
        // 例如：设置默认配置、注册全局事件监听器等
        await this.setupDefaultConfiguration(uiAdapter);
      } else {
        Log.showError(TAG, 'BiShare服务初始化失败');
      }

      return isInitSuccess;
    } catch (error) {
      Log.showError(TAG, `initBiShareService failed: ${error}`);
      throw new Error("initBiShareService failed: " + error);
    }
  }

  /**
   * 设置默认配置
   */
  private async setupDefaultConfiguration(uiAdapter: UIAdapter): Promise<void> {
    try {
      Log.showInfo(TAG, '设置默认配置...');

      // 这里可以添加一些默认配置
      // 例如：设置超时时间、重试次数等

      Log.showInfo(TAG, '默认配置设置完成');
    } catch (error) {
      Log.showWarn(TAG, `设置默认配置失败: ${error}`);
      // 不抛出异常，因为这不是关键错误
    }
  }

  public async destroy(): Promise<void> {
    try {
      Log.showInfo(TAG, '开始销毁BiShare服务...');

      const uiAdapter = UIAdapter.getInstance();
      const success = await uiAdapter.release();

      if (success) {
        Log.showInfo(TAG, 'BiShare服务销毁成功');
      } else {
        Log.showWarn(TAG, 'BiShare服务销毁可能未完全成功');
      }
    } catch (error) {
      Log.showError(TAG, `销毁BiShare服务失败: ${error}`);
      // 不抛出异常，因为销毁操作应该尽量完成
    }
  }

  /**
   * 获取服务状态
   */
  public getServiceStatus(): boolean {
    try {
      const uiAdapter = UIAdapter.getInstance();
      return uiAdapter.getInitializationStatus();
    } catch (error) {
      Log.showError(TAG, `获取服务状态失败: ${error}`);
      return false;
    }
  }

  /**
   * 获取UIAdapter实例（供外部使用）
   */
  public getUIAdapter(): UIAdapter {
    return UIAdapter.getInstance();
  }
}