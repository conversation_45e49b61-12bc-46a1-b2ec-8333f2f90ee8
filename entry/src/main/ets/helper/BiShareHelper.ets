import { BlogPriority, InitOptions } from '@ohos/libbishare_napi';
import common from '@ohos.app.ability.common';
import { Log } from '@ohos/lib_info_sender';
import { UIAdapter } from '../comm/adapters/UIAdapter';


const TAG = 'BiShareHelper';
/**
 * BiShare工具类
 */
export class BiShareHelper {

  private static sInstance: BiShareHelper;

  private constructor() {
  }

  public static getInstance() {
    if (!BiShareHelper.sInstance) {
      BiShareHelper.sInstance = new BiShareHelper();
    }
    return BiShareHelper.sInstance;
  }

  public async initBiShareService(context: common.ApplicationContext): Promise<boolean> {
    const initOptions: InitOptions = {
      isConsole: true,
      isFile: true,
      filePath: context.filesDir + "/" + "service_" + Date.parse(new Date().toString()) + ".log",
      priority: BlogPriority.INFO
    };

    try {
      const uiAdapter = UIAdapter.getInstance();
      const isInitSuccess = await uiAdapter.initialize(context, initOptions);
      Log.showInfo(TAG, `initBiShareService isInitSuccess: ${isInitSuccess}`);
      return isInitSuccess;
    } catch (error) {
      Log.showError(TAG, `initBiShareService failed: ${error}`);
      throw new Error("initBiShareService failed: " + error);
    }
  }

  public async destroy(): Promise<void> {
    const uiAdapter = UIAdapter.getInstance();
    await uiAdapter.release();
  }
}