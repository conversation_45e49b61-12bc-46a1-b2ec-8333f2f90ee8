{"string": [{"name": "module_desc", "value": "module description"}, {"name": "EntryAbility_desc", "value": "description"}, {"name": "EntryAbility_label", "value": "label"}, {"name": "boe_share", "value": "BOE Share"}, {"name": "host_name", "value": "host name: "}, {"name": "mobile_computer_cast", "value": "mobile/computer_cast"}, {"name": "one", "value": "1"}, {"name": "two", "value": "2"}, {"name": "three", "value": "3"}, {"name": "download_software", "value": "download software"}, {"name": "connect_network", "value": "connect network"}, {"name": "start_cast", "value": "start cast"}, {"name": "scan_input_download", "value": "Scan the code on your phone or enter the website on your computer to download"}, {"name": "conn_to_cast_network", "value": "Connect your phone and computer to the screen mirroring network"}, {"name": "choose_cast_method", "value": "Select screen casting method to start screen casting"}, {"name": "screen_cast", "value": "Screen Cast"}, {"name": "insert_large_screen", "value": "Insert large screen"}, {"name": "insert_computer", "value": "Insert into computer"}, {"name": "press_key", "value": "Press the key"}, {"name": "wait_five_seconds_for_match", "value": "Wait for 5 seconds to complete pairing"}, {"name": "green_light_conn_success", "value": "Green light stays on, connection successful"}, {"name": "will_see_cast_screen", "value": "You can see the screen projection screen"}, {"name": "more", "value": "more"}, {"name": "conn_devices", "value": "Connected devices %d units"}, {"name": "conn_times", "value": "Connection times: %d times"}, {"name": "web_cast", "value": "Web page projection"}, {"name": "visitor_recommendations", "value": "Visitor recommendations"}, {"name": "computer_input_website_address", "value": "Computer input website address"}, {"name": "input_conn_code", "value": "Enter the connection code"}, {"name": "get", "value": "get"}, {"name": "warm_reminder", "value": "warm reminder"}, {"name": "make_sure_computer_conn", "value": "Please make sure the computer is connected to the Internet!"}, {"name": "file_receive", "value": "files receiving"}, {"name": "setting", "value": "setting"}]}