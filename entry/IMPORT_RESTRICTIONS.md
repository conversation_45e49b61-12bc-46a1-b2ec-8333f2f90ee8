# 导入限制配置说明

## 🎯 目标

限制entry模块中使用相对路径导入bishare模块，强制使用标准的模块导入方式。

## ❌ 禁止的导入方式

```typescript
// ❌ 禁止使用相对路径导入
import { NetworkInfoOptions } from '../../../../../bishare/Index';
import { NetworkType } from '../../bishare/src/main/ets/interfaces/BiShareTypes';
import { BiShareManager } from '../../../bishare/src/main/ets/core/BiShareManager';
```

## ✅ 推荐的导入方式

```typescript
// ✅ 使用标准模块导入
import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';
import { BiShareManager } from '@ohos/libbishare_napi';
```

## 🛠️ 实现方案

### 方案1：编译时检查脚本 (推荐)

**文件**: `entry/scripts/check-imports.js`

- 在编译前自动检查所有.ets和.ts文件
- 发现禁止的导入模式时终止编译
- 提供详细的错误信息和修复建议

**使用方法**:
```bash
# 手动执行检查
cd entry
node scripts/check-imports.js

# 编译时自动执行（已集成到hvigorfile.ts）
./hvigorw assembleHap --mode module -p module=entry
```

### 方案2：Shell脚本检查

**文件**: `entry/scripts/pre-build.sh`

- 使用grep命令快速检查文件
- 轻量级，无需Node.js依赖
- 集成到build-profile.json5中

**使用方法**:
```bash
# 手动执行
cd entry
./scripts/pre-build.sh

# 编译时自动执行
./hvigorw assembleHap --mode module -p module=entry
```

### 方案3：ESLint规则

**文件**: `entry/.eslintrc.js`

- 使用ESLint的no-restricted-imports规则
- IDE实时提示错误
- 需要安装ESLint插件

**配置**:
```javascript
'no-restricted-imports': [
  'error',
  {
    patterns: [
      {
        group: ['**/bishare/**'],
        message: '禁止使用相对路径导入bishare模块，请使用 @ohos/libbishare_napi 代替'
      }
    ]
  }
]
```

### 方案4：TypeScript配置

**文件**: `entry/tsconfig.json`

- 使用路径映射强制标准导入
- 编译时类型检查
- IDE智能提示支持

**配置**:
```json
{
  "compilerOptions": {
    "paths": {
      "@ohos/libbishare_napi": ["../bishare/src/main/ets/index"],
      "**/bishare/**": ["./forbidden-path-placeholder"]
    }
  }
}
```

## 🔧 集成配置

### hvigorfile.ts集成

已在`entry/hvigorfile.ts`中集成导入检查插件：

```typescript
const importCheckPlugin = {
  pluginId: 'importCheckPlugin',
  apply(pluginContext) {
    pluginContext.registerTask({
      name: 'checkImports',
      run: (taskContext) => {
        // 执行导入规范检查
        execSync(`node "${scriptPath}"`, { stdio: 'inherit' });
      }
    });
  }
};
```

### build-profile.json5集成

已在构建配置中添加编译前检查：

```json5
{
  "buildOption": {
    "arkOptions": {
      "compilerOptions": {
        "strictImportChecks": true,
        "noRelativeImports": ["**/bishare/**"]
      }
    },
    "preBuildScript": "./scripts/pre-build.sh"
  }
}
```

## 🚀 使用效果

### 编译时检查

当发现禁止的导入时，编译会失败并显示详细错误信息：

```bash
🔍 执行导入规范检查...

❌ /path/to/file.ets:
   第15行: import { NetworkInfoOptions } from '../../../../../bishare/Index';
   禁止使用相对路径导入bishare模块

❌ 检测到不规范的导入方式！

禁止使用相对路径导入bishare模块：
  ❌ import { NetworkInfoOptions } from '../../../../../bishare/Index';

请使用标准的模块导入方式：
  ✅ import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';

❌ 发现 1 个导入规范问题，请修复后重新编译！
```

### IDE提示

如果配置了ESLint，IDE会实时显示错误提示：

```
ESLint: 禁止使用相对路径导入bishare模块，请使用 @ohos/libbishare_napi 代替 (no-restricted-imports)
```

## 📋 检查清单

在提交代码前，请确保：

- [ ] 所有bishare模块的导入都使用`@ohos/libbishare_napi`
- [ ] 没有使用相对路径导入bishare模块
- [ ] 编译检查脚本执行通过
- [ ] ESLint检查无错误（如果启用）

## 🔍 故障排除

### 问题1：检查脚本执行失败

**解决方案**:
```bash
# 确保脚本有执行权限
chmod +x entry/scripts/pre-build.sh

# 检查Node.js是否安装
node --version
```

### 问题2：路径映射不生效

**解决方案**:
- 检查tsconfig.json中的paths配置
- 确保baseUrl设置正确
- 重启IDE或TypeScript服务

### 问题3：ESLint规则不生效

**解决方案**:
```bash
# 安装必要的依赖
npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin

# 检查.eslintrc.js配置
```

## 📚 参考资料

- [OpenHarmony模块化开发指南](https://docs.openharmony.cn/)
- [ESLint no-restricted-imports规则](https://eslint.org/docs/rules/no-restricted-imports)
- [TypeScript路径映射](https://www.typescriptlang.org/docs/handbook/module-resolution.html#path-mapping)
- [Hvigor构建工具](https://developer.harmonyos.com/cn/docs/documentation/doc-guides/hvigor-0000001055701144)
