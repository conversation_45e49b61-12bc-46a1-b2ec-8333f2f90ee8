# BiShare错误码使用指南

## 📋 概述

BiShare框架提供了完整的错误码系统，帮助开发者准确识别和处理各种错误情况。错误码在C++层定义，通过NAPI接口导出到JavaScript层，确保前后端错误处理的一致性。

## 🔢 错误码定义

### C++层错误码定义

在 `bishare_status_codes.h` 中定义：

```cpp
// 基础错误码（来自bishare-define.h）
#define BS_OK                    0   // 成功
#define BS_NOT_INIT              1   // 未初始化
#define BS_NOT_FOUND             2   // 未找到
#define BS_PARAMS_ERROR          3   // 参数错误
#define BS_OPS_ERROR             5   // 操作错误
#define BS_TIMEOUT               7   // 超时
#define BS_ALLOCATE_ERROR        8   // 分配错误
#define BS_SEND_ERROR           10   // 发送错误
#define BS_RECV_ERROR           11   // 接收错误

// 扩展错误码（映射到基础错误码）
#define BS_ERROR                 BS_OPS_ERROR           // 一般错误
#define BS_INVALID_PARAM         BS_PARAMS_ERROR        // 无效参数
#define BS_NOT_SUPPORTED         BS_NOT_FOUND           // 不支持的操作
#define BS_PERMISSION_DENIED     BS_OPS_ERROR           // 权限拒绝
#define BS_DEVICE_NOT_FOUND      BS_NOT_FOUND           // 设备未找到
#define BS_CONNECTION_FAILED     BS_OPS_ERROR           // 连接失败
#define BS_INSUFFICIENT_MEMORY   BS_ALLOCATE_ERROR      // 内存不足
```

### JavaScript层错误码常量

通过NAPI接口导出到JavaScript：

```typescript
// 从原生模块导入
import bishare from 'libbishare_napi.so';

// 错误码常量（由原生模块提供）
console.log(bishare.BS_OK);                    // 0
console.log(bishare.BS_ERROR);                 // -1
console.log(bishare.BS_NOT_INIT);              // -2
console.log(bishare.BS_INVALID_PARAM);         // -3
console.log(bishare.BS_NOT_SUPPORTED);         // -4
console.log(bishare.BS_PERMISSION_DENIED);     // -5
console.log(bishare.BS_DEVICE_NOT_FOUND);      // -6
console.log(bishare.BS_CONNECTION_FAILED);     // -7
console.log(bishare.BS_TIMEOUT);               // -8
console.log(bishare.BS_INSUFFICIENT_MEMORY);   // -9
```

## 🛠️ 使用方式

### 1. 基础错误检查

```typescript
import { ErrorCodes, BiShareErrorUtils } from '../types/bishare_error_codes';

async function initializeBiShare() {
  try {
    const result = await bishare.initialize(true, true, "/data/logs", 3);
    
    // 方式1: 直接检查success字段
    if (result.success) {
      console.log('初始化成功');
    } else {
      console.error('初始化失败:', result.message);
    }
    
    // 方式2: 使用错误码检查
    if (BiShareErrorUtils.isSuccess(result.code)) {
      console.log('初始化成功');
    } else {
      console.error('初始化失败, 错误码:', result.code);
    }
    
  } catch (error) {
    console.error('初始化异常:', error);
  }
}
```

### 2. 详细错误处理

```typescript
import { ErrorCodes, BiShareErrorUtils, BiShareError } from '../types/bishare_error_codes';

async function handleBiShareOperation() {
  try {
    const result = await bishare.discoverDevices();
    
    if (!result.success) {
      // 根据错误码进行不同处理
      switch (result.code) {
        case ErrorCodes.BS_NOT_INIT:
          console.error('BiShare未初始化，请先调用initialize()');
          // 尝试自动初始化
          await initializeBiShare();
          break;
          
        case ErrorCodes.BS_PERMISSION_DENIED:
          console.error('权限不足，请检查应用权限设置');
          // 引导用户设置权限
          showPermissionDialog();
          break;
          
        case ErrorCodes.BS_DEVICE_NOT_FOUND:
          console.error('设备未找到，请检查设备连接');
          // 提示用户检查设备
          showDeviceCheckDialog();
          break;
          
        case ErrorCodes.BS_TIMEOUT:
          console.error('操作超时，请重试');
          // 提供重试选项
          showRetryDialog();
          break;
          
        case ErrorCodes.BS_INSUFFICIENT_MEMORY:
          console.error('内存不足，请关闭其他应用');
          // 提示用户释放内存
          showMemoryWarning();
          break;
          
        default:
          console.error('未知错误:', BiShareErrorUtils.getErrorMessage(result.code));
      }
    }
    
  } catch (error) {
    if (error instanceof BiShareError) {
      // 处理BiShare特定错误
      console.error('BiShare错误:', error.message, '错误码:', error.code);
      
      if (error.isInitError()) {
        console.log('这是初始化相关错误');
      } else if (error.isNetworkError()) {
        console.log('这是网络相关错误');
      }
    } else {
      // 处理其他错误
      console.error('系统错误:', error);
    }
  }
}
```

### 3. 错误分类处理

```typescript
import { BiShareErrorUtils } from '../types/bishare_error_codes';

function handleErrorByCategory(code: number) {
  if (BiShareErrorUtils.isInitError(code)) {
    // 初始化错误处理
    console.log('处理初始化错误');
    return handleInitError(code);
  }
  
  if (BiShareErrorUtils.isParamError(code)) {
    // 参数错误处理
    console.log('处理参数错误');
    return handleParamError(code);
  }
  
  if (BiShareErrorUtils.isNetworkError(code)) {
    // 网络错误处理
    console.log('处理网络错误');
    return handleNetworkError(code);
  }
  
  if (BiShareErrorUtils.isResourceError(code)) {
    // 资源错误处理
    console.log('处理资源错误');
    return handleResourceError(code);
  }
  
  // 其他错误处理
  console.log('处理其他错误');
  return handleGenericError(code);
}
```

### 4. 统一错误处理器

```typescript
import { BiShareResult, BiShareErrorUtils, ErrorCodes } from '../types/bishare_error_codes';

class BiShareErrorHandler {
  /**
   * 统一的错误处理方法
   */
  static async handleResult(result: BiShareResult): Promise<boolean> {
    if (result.success) {
      return true;
    }
    
    // 记录错误日志
    console.error(`BiShare操作失败: ${result.message} (错误码: ${result.code})`);
    
    // 根据错误类型进行处理
    switch (result.code) {
      case ErrorCodes.BS_NOT_INIT:
        return await this.handleNotInitError();
        
      case ErrorCodes.BS_PERMISSION_DENIED:
        return await this.handlePermissionError();
        
      case ErrorCodes.BS_TIMEOUT:
        return await this.handleTimeoutError();
        
      default:
        return await this.handleGenericError(result);
    }
  }
  
  private static async handleNotInitError(): Promise<boolean> {
    console.log('尝试重新初始化BiShare服务...');
    try {
      const result = await bishare.initialize(true, true, "/data/logs", 3);
      return result.success;
    } catch (error) {
      console.error('重新初始化失败:', error);
      return false;
    }
  }
  
  private static async handlePermissionError(): Promise<boolean> {
    console.log('权限错误，需要用户手动处理');
    // 这里可以显示权限设置引导
    return false;
  }
  
  private static async handleTimeoutError(): Promise<boolean> {
    console.log('操作超时，可以考虑重试');
    // 这里可以实现自动重试逻辑
    return false;
  }
  
  private static async handleGenericError(result: BiShareResult): Promise<boolean> {
    console.log('通用错误处理:', result.message);
    return false;
  }
}

// 使用示例
async function safeOperation() {
  const result = await bishare.discoverDevices();
  const handled = await BiShareErrorHandler.handleResult(result);
  
  if (handled) {
    console.log('操作成功或错误已处理');
  } else {
    console.log('操作失败且无法自动处理');
  }
}
```

### 5. 自定义错误类使用

```typescript
import { BiShareError, ErrorCodes } from '../types/bishare_error_codes';

function throwBiShareError(code: number, customMessage?: string) {
  throw new BiShareError(code, customMessage);
}

try {
  // 模拟抛出错误
  throwBiShareError(ErrorCodes.BS_DEVICE_NOT_FOUND, '指定的设备ID不存在');
  
} catch (error) {
  if (error instanceof BiShareError) {
    console.log('错误码:', error.code);
    console.log('错误消息:', error.message);
    
    // 使用便捷方法检查错误类型
    if (error.isCode(ErrorCodes.BS_DEVICE_NOT_FOUND)) {
      console.log('这是设备未找到错误');
    }
    
    if (error.isNetworkError()) {
      console.log('这是网络相关错误');
    }
  }
}
```

## 🎯 最佳实践

### 1. 错误码映射

```typescript
// 创建错误码到用户友好消息的映射
const UserFriendlyMessages = {
  [ErrorCodes.BS_OK]: "操作成功",
  [ErrorCodes.BS_NOT_INIT]: "服务未启动，请稍后重试",
  [ErrorCodes.BS_PERMISSION_DENIED]: "权限不足，请在设置中开启相关权限",
  [ErrorCodes.BS_DEVICE_NOT_FOUND]: "未找到设备，请检查设备连接",
  [ErrorCodes.BS_CONNECTION_FAILED]: "连接失败，请检查网络设置",
  [ErrorCodes.BS_TIMEOUT]: "操作超时，请重试",
  [ErrorCodes.BS_INSUFFICIENT_MEMORY]: "内存不足，请关闭其他应用后重试"
};

function getUserFriendlyMessage(code: number): string {
  return UserFriendlyMessages[code] || "操作失败，请重试";
}
```

### 2. 错误重试机制

```typescript
async function retryableOperation(operation: () => Promise<any>, maxRetries: number = 3): Promise<any> {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await operation();
      
      if (result.success) {
        return result;
      }
      
      // 只对特定错误进行重试
      if (result.code === ErrorCodes.BS_TIMEOUT || 
          result.code === ErrorCodes.BS_CONNECTION_FAILED) {
        console.log(`操作失败，第${i + 1}次重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // 递增延迟
        continue;
      }
      
      // 其他错误不重试
      return result;
      
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error;
      }
      console.log(`操作异常，第${i + 1}次重试...`);
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}

// 使用示例
const result = await retryableOperation(() => bishare.discoverDevices());
```

### 3. 错误监控和上报

```typescript
class BiShareErrorMonitor {
  private static errorCounts: Map<number, number> = new Map();
  
  static reportError(code: number, context: string) {
    // 统计错误次数
    const count = this.errorCounts.get(code) || 0;
    this.errorCounts.set(code, count + 1);
    
    // 记录错误日志
    console.error(`BiShare错误 [${context}]: ${BiShareErrorUtils.getErrorMessage(code)} (错误码: ${code})`);
    
    // 上报到监控系统（示例）
    this.uploadErrorToMonitoring(code, context);
  }
  
  private static uploadErrorToMonitoring(code: number, context: string) {
    // 这里可以集成实际的监控系统
    console.log(`上报错误到监控系统: 错误码=${code}, 上下文=${context}`);
  }
  
  static getErrorStatistics() {
    return Array.from(this.errorCounts.entries()).map(([code, count]) => ({
      code,
      message: BiShareErrorUtils.getErrorMessage(code),
      count
    }));
  }
}
```

## 📝 总结

BiShare的错误码系统提供了：

1. **一致性**: C++和JavaScript层使用相同的错误码定义
2. **类型安全**: TypeScript类型定义确保编译时检查
3. **易用性**: 丰富的工具函数简化错误处理
4. **扩展性**: 支持自定义错误处理逻辑
5. **可维护性**: 统一的错误处理模式

通过合理使用错误码系统，可以大大提升应用的稳定性和用户体验。
