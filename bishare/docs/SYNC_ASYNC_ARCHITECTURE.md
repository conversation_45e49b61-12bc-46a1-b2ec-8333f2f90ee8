# BiShare 同步/异步架构设计文档

## 🎯 **设计目标**

### **核心原则**
1. **最小化代码改动** - 保持现有代码结构不变
2. **向后兼容** - 现有同步调用继续工作
3. **智能检测** - 自动识别同步/异步模式
4. **性能优化** - 耗时操作不阻塞主线程

### **架构概览**

```
JavaScript调用
    ↓
NAPI接口层 (bishare_napi_interface.cpp)
    ↓
SyncAsyncAdapter (智能适配器)
    ↓                    ↓
同步执行路径          异步执行路径
    ↓                    ↓
现有Operation类      AsyncExecutor
    ↓                    ↓
原生BiShare服务      后台线程执行
```

## 🔧 **核心组件**

### **1. SyncAsyncAdapter (同步/异步适配器)**

#### **职责**
- 检测调用模式（同步/异步）
- 路由到相应的执行路径
- 提供统一的错误处理
- 包装现有同步方法为异步执行

#### **关键方法**
```cpp
class SyncAsyncAdapter {
public:
    // 智能执行 - 自动检测模式
    static napi_value SmartExecute(
        napi_env env, 
        napi_callback_info info,
        const std::string& resourceName,
        SyncFunction syncFunc,
        AsyncExecuteFunction asyncExecuteFunc = nullptr,
        AsyncCompleteFunction asyncCompleteFunc = nullptr
    );
    
    // 包装同步方法为异步执行
    static napi_value WrapSyncAsAsync(
        napi_env env, 
        napi_callback_info info,
        const std::string& resourceName,
        SyncFunction syncFunc
    );
};
```

#### **模式检测逻辑**
```cpp
bool SyncAsyncAdapter::HasCallbackParameter(napi_env env, napi_callback_info info) {
    size_t argc = 10;
    napi_value argv[10];
    napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);
    
    if (argc == 0) return false;
    
    // 检查最后一个参数是否为函数
    napi_valuetype valueType;
    napi_status status = napi_typeof(env, argv[argc - 1], &valueType);
    
    return (status == napi_ok && valueType == napi_function);
}
```

### **2. AsyncExecutor (异步执行器)**

#### **职责**
- 管理NAPI异步工作队列
- 支持Promise和回调两种模式
- 提供统一的异步执行框架
- 处理异步操作的生命周期

#### **执行流程**
```cpp
// 1. 创建异步工作数据
auto workData = new AsyncWorkData(env, resourceName);

// 2. 设置Promise或回调
if (usePromise) {
    workData->deferred = deferred;
} else {
    workData->callback = callback;
}

// 3. 创建并队列异步工作
napi_create_async_work(env, nullptr, resourceNameValue,
    ExecuteCallback,     // 后台线程
    CompleteCallback,    // 主线程
    workData, &workData->work);

napi_queue_async_work(env, workData->work);
```

### **3. 现有Operation类 (保持不变)**

#### **优势**
- ✅ 无需修改现有代码
- ✅ 保持现有的错误处理逻辑
- ✅ 维持现有的参数解析机制
- ✅ 继续使用现有的日志记录

#### **集成方式**
```cpp
// 现有Operation类作为同步执行函数
napi_value BiShareNapiInterface::DiscoverDevices(napi_env env, napi_callback_info info) {
    return SMART_EXECUTE("DiscoverDevices", 
        [](napi_env env, napi_callback_info info) -> napi_value {
            auto operation = std::make_unique<DiscoverDevicesOperation>();
            return operation->Execute(env, info);  // 现有代码不变
        }
    );
}
```

## 📊 **执行路径分析**

### **同步执行路径**
```
JavaScript: biShare.discoverDevices()
    ↓
NAPI: BiShareNapiInterface::DiscoverDevices()
    ↓
SyncAsyncAdapter::SmartExecute() [检测到无回调]
    ↓
SyncAsyncAdapter::ExecuteSync()
    ↓
DiscoverDevicesOperation::Execute() [现有代码]
    ↓
bishare_service_discover_devices() [原生服务]
    ↓
直接返回结果给JavaScript
```

### **异步执行路径 (Promise)**
```
JavaScript: await biShare.discoverDevices()
    ↓
NAPI: BiShareNapiInterface::DiscoverDevices()
    ↓
SyncAsyncAdapter::SmartExecute() [检测到Promise]
    ↓
AsyncExecutor::ExecuteAsyncAuto()
    ↓
创建Promise和异步工作
    ↓
后台线程: 执行DiscoverDevicesOperation
    ↓
主线程: 解析Promise并返回结果
```

### **异步执行路径 (回调)**
```
JavaScript: biShare.discoverDevices(callback)
    ↓
NAPI: BiShareNapiInterface::DiscoverDevices()
    ↓
SyncAsyncAdapter::SmartExecute() [检测到回调函数]
    ↓
AsyncExecutor::ExecuteAsyncAuto()
    ↓
创建异步工作并保存回调
    ↓
后台线程: 执行DiscoverDevicesOperation
    ↓
主线程: 调用回调函数
```

## 🔄 **三种集成模式**

### **模式1: 智能检测 (推荐)**
```cpp
// 自动检测同步/异步，使用现有Operation
return SMART_EXECUTE("DiscoverDevices", 
    [](napi_env env, napi_callback_info info) -> napi_value {
        auto operation = std::make_unique<DiscoverDevicesOperation>();
        return operation->Execute(env, info);
    }
);
```

**优势**: 最小代码改动，自动适配

### **模式2: 专门异步实现**
```cpp
// 提供专门的异步执行和完成函数
return SMART_EXECUTE_WITH_ASYNC("GetDiscoveredDevices",
    syncFunc,           // 同步函数
    asyncExecuteFunc,   // 异步执行函数
    asyncCompleteFunc   // 异步完成函数
);
```

**优势**: 异步性能最优，可以在后台线程执行原生调用

### **模式3: 包装异步**
```cpp
// 将同步函数包装为异步执行
return WRAP_SYNC_AS_ASYNC("StartScreenRecord",
    [](napi_env env, napi_callback_info info) -> napi_value {
        auto operation = std::make_unique<StartScreenRecordOperation>();
        return operation->Execute(env, info);
    }
);
```

**优势**: 强制异步执行，适合耗时操作

## 🎯 **实施策略**

### **阶段1: 核心方法 (已完成)**
- ✅ `DiscoverDevices` - 智能检测模式
- ✅ `GetDiscoveredDevices` - 专门异步实现
- ✅ `StartScreenRecord` - 包装异步模式

### **阶段2: 扩展方法 (建议)**
```cpp
// 设备管理
SetDeviceInfo        → 智能检测 (快速操作)
FindRemoteDevice     → 包装异步 (可能耗时)

// 录制管理  
StopScreenRecord     → 包装异步 (耗时操作)
Screenshot           → 包装异步 (I/O操作)

// 网络管理
SetNetworkInfo       → 智能检测 (配置操作)
GetRootPath          → 智能检测 (快速查询)
```

### **阶段3: 全面覆盖**
- 所有NAPI方法都支持双模式
- 性能优化和测试
- 文档完善

## 📈 **性能影响**

### **同步模式**
- **开销**: 几乎无额外开销
- **延迟**: 与原有实现相同
- **内存**: 无额外内存分配

### **异步模式**
- **开销**: 异步工作队列管理
- **延迟**: 线程切换开销 (~1-2ms)
- **内存**: AsyncWorkData结构 (~100字节)
- **优势**: 不阻塞主线程

### **智能检测**
- **开销**: 参数类型检查 (~0.1ms)
- **优势**: 自动选择最优路径

## 🔒 **线程安全**

### **同步模式**
- 在主线程执行，与现有代码相同
- 使用现有的线程安全机制

### **异步模式**
- 后台线程执行原生调用
- 主线程处理NAPI操作
- AsyncWorkData提供线程间数据传递

### **注意事项**
- 原生BiShare服务需要线程安全
- NAPI对象只能在主线程访问
- 异步完成回调在主线程执行

## 🎉 **总结**

这个同步/异步双模式架构实现了：

1. **✅ 最小化改动** - 现有代码基本不变
2. **✅ 向后兼容** - 现有调用继续工作  
3. **✅ 性能优化** - 耗时操作不阻塞UI
4. **✅ 开发友好** - 支持现代Promise和传统回调
5. **✅ 架构清晰** - 职责分离，易于维护

通过智能适配器模式，我们在保持现有代码稳定的同时，为BiShare NAPI接口添加了强大的异步支持能力！
