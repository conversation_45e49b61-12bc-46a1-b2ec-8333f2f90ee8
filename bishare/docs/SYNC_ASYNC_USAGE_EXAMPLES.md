# BiShare 同步/异步双模式使用指南

## 📋 **功能概述**

BiShare NAPI接口现在支持同步和异步两种调用模式：

- **同步模式**: 直接返回结果，适合快速操作
- **异步模式**: 支持Promise和回调，适合耗时操作
- **自动检测**: 根据参数自动选择模式
- **完整实现**: 真正的异步执行，而非简单包装

## 🎯 **API分类说明**

### **智能检测模式 (SMART_EXECUTE)**
适用于配置和查询操作，根据是否有回调参数自动选择同步/异步：
- `setDeviceInfo` - 设备信息配置
- `getDeviceModel` - 设备模型查询
- `setNetworkInfo` - 网络配置
- `getRootPath` - 路径查询

### **专门异步实现 (完整异步)**
提供真正的后台线程执行，适用于可能耗时的操作：
- `getDiscoveredDevices` - 获取设备列表
- `startScreenRecord` - 启动录制

### **包装异步模式 (WRAP_SYNC_AS_ASYNC)**
强制异步执行，适用于I/O密集型操作：
- `stopScreenRecord` - 停止录制
- `screenshot` - 截图操作
- `startCapture` - 启动捕获

## 🎯 **使用方式**

### **1. 同步调用（无回调参数）**

```javascript
// 同步调用 - 直接返回结果
try {
    const devices = biShare.getDiscoveredDevices();
    console.log('发现的设备:', devices);
} catch (error) {
    console.error('获取设备失败:', error);
}

// 同步设备发现
try {
    const result = biShare.discoverDevices();
    console.log('设备发现启动:', result);
} catch (error) {
    console.error('设备发现失败:', error);
}
```

### **2. 异步调用 - Promise模式**

```javascript
// Promise模式 - 现代JavaScript推荐方式
async function discoverDevicesAsync() {
    try {
        // 异步设备发现
        const result = await biShare.discoverDevices();
        console.log('设备发现启动成功:', result);
        
        // 异步获取设备列表
        const devices = await biShare.getDiscoveredDevices();
        console.log('发现的设备:', devices);
        
        // 异步录制
        const recordResult = await biShare.startScreenRecord({
            session: 1,
            displayId: 0,
            direction: 0
        });
        console.log('录制启动成功:', recordResult);
        
    } catch (error) {
        console.error('异步操作失败:', error);
    }
}

// 调用异步函数
discoverDevicesAsync();
```

### **3. 异步调用 - 回调模式**

```javascript
// 回调模式 - 传统JavaScript方式
biShare.discoverDevices((error, result) => {
    if (error) {
        console.error('设备发现失败:', error);
        return;
    }
    console.log('设备发现启动成功:', result);
    
    // 获取设备列表
    biShare.getDiscoveredDevices((error, devices) => {
        if (error) {
            console.error('获取设备失败:', error);
            return;
        }
        console.log('发现的设备:', devices);
    });
});

// 录制操作回调
biShare.startScreenRecord({
    session: 1,
    displayId: 0,
    direction: 0
}, (error, result) => {
    if (error) {
        console.error('录制启动失败:', error);
        return;
    }
    console.log('录制启动成功:', result);
});
```

### **4. 完整的设备发现和录制流程**

```javascript
// 完整的设备发现和录制流程示例
class BiShareDeviceManager {
    constructor() {
        this.isInitialized = false;
        this.discoveredDevices = [];
        this.isRecording = false;
    }

    // 初始化服务（同步）
    async initialize() {
        try {
            // 同步初始化
            const result = biShare.initialize({
                isConsole: true,
                isFile: false,
                priority: biShare.LOG_PRIORITY_INFO
            });
            this.isInitialized = result;
            console.log('BiShare初始化:', result ? '成功' : '失败');
            return result;
        } catch (error) {
            console.error('初始化失败:', error);
            return false;
        }
    }

    // 设备发现流程（异步）
    async discoverDevices() {
        if (!this.isInitialized) {
            throw new Error('服务未初始化');
        }

        try {
            console.log('开始设备发现...');

            // 异步启动设备发现
            await biShare.discoverDevices();
            console.log('设备发现启动成功');

            // 等待一段时间让设备发现完成
            await new Promise(resolve => setTimeout(resolve, 3000));

            // 异步获取发现的设备列表
            const devices = await biShare.getDiscoveredDevices();
            this.discoveredDevices = devices;
            console.log('发现设备:', devices);

            return devices;
        } catch (error) {
            console.error('设备发现失败:', error);
            throw error;
        }
    }

    // 设备发现流程（回调模式）
    discoverDevicesWithCallback(callback) {
        if (!this.isInitialized) {
            callback(new Error('服务未初始化'), null);
            return;
        }

        console.log('开始设备发现（回调模式）...');

        // 使用回调模式启动设备发现
        biShare.discoverDevices((error, result) => {
            if (error) {
                console.error('设备发现启动失败:', error);
                callback(error, null);
                return;
            }

            console.log('设备发现启动成功');

            // 等待一段时间后获取设备列表
            setTimeout(() => {
                biShare.getDiscoveredDevices((error, devices) => {
                    if (error) {
                        console.error('获取设备列表失败:', error);
                        callback(error, null);
                        return;
                    }

                    this.discoveredDevices = devices;
                    console.log('发现设备:', devices);
                    callback(null, devices);
                });
            }, 3000);
        });
    }

    // 录制操作（异步Promise模式）
    async startRecording(options = {}) {
        if (!this.isInitialized) {
            throw new Error('服务未初始化');
        }

        if (this.isRecording) {
            throw new Error('录制已在进行中');
        }

        try {
            console.log('开始录制...', options);

            // 异步启动录制
            const result = await biShare.startScreenRecord({
                session: options.session || 1,
                displayId: options.displayId || 0,
                direction: options.direction || 0,
                ...options
            });

            this.isRecording = true;
            console.log('录制启动成功:', result);
            return result;
        } catch (error) {
            console.error('录制启动失败:', error);
            throw error;
        }
    }

    // 停止录制（异步）
    async stopRecording() {
        if (!this.isRecording) {
            throw new Error('当前没有进行录制');
        }

        try {
            console.log('停止录制...');

            // 异步停止录制
            const result = await biShare.stopScreenRecord({
                session: 1,
                displayId: 0,
                direction: 0
            });

            this.isRecording = false;
            console.log('录制停止成功:', result);
            return result;
        } catch (error) {
            console.error('录制停止失败:', error);
            throw error;
        }
    }

    // 录制操作（回调模式）
    startRecordingWithCallback(options = {}, callback) {
        if (!this.isInitialized) {
            callback(new Error('服务未初始化'), null);
            return;
        }

        if (this.isRecording) {
            callback(new Error('录制已在进行中'), null);
            return;
        }

        console.log('开始录制（回调模式）...', options);

        biShare.startScreenRecord({
            session: options.session || 1,
            displayId: options.displayId || 0,
            direction: options.direction || 0,
            ...options
        }, (error, result) => {
            if (error) {
                console.error('录制启动失败:', error);
                callback(error, null);
                return;
            }

            this.isRecording = true;
            console.log('录制启动成功:', result);
            callback(null, result);
        });
    }

    // 快速配置操作（同步模式）
    setDeviceInfo(name, password) {
        try {
            // 同步设置设备信息
            return biShare.setDeviceInfo(name, password);
        } catch (error) {
            console.error('设置设备信息失败:', error);
            return false;
        }
    }

    // 快速查询操作（同步模式）
    getCurrentDirectory() {
        try {
            return biShare.getCurrentDirector();
        } catch (error) {
            console.error('获取当前目录失败:', error);
            return null;
        }
    }
}

// 使用示例
const manager = new BiShareManager();

// 同步初始化
if (manager.initialize()) {
    console.log('BiShare初始化成功');
    
    // 异步设备发现
    manager.discoverDevices()
        .then(devices => {
            console.log('发现设备:', devices);
            
            // 异步录制
            return manager.startRecording({
                session: 1,
                displayId: 0,
                direction: 0
            });
        })
        .then(result => {
            console.log('录制启动成功:', result);
        })
        .catch(error => {
            console.error('操作失败:', error);
        });
    
    // 同步获取目录
    const currentDir = manager.getCurrentDirectory();
    console.log('当前目录:', currentDir);
}
```

## 🔧 **性能建议**

### **何时使用同步模式**
- ✅ 快速的配置操作（如设置参数）
- ✅ 简单的查询操作（如获取状态）
- ✅ 初始化和清理操作
- ✅ 不涉及网络或文件I/O的操作

### **何时使用异步模式**
- ✅ 设备发现和连接操作
- ✅ 录制和截图操作
- ✅ 文件传输操作
- ✅ 网络相关操作
- ✅ 任何可能耗时的操作

### **最佳实践**

```javascript
// ✅ 推荐：根据操作特性选择模式
class BiShareBestPractices {
    // 同步：快速配置
    setDeviceInfo(info) {
        return biShare.setDeviceInfo(info);
    }
    
    // 异步：耗时操作
    async discoverAndConnect(deviceId) {
        await biShare.discoverDevices();
        const devices = await biShare.getDiscoveredDevices();
        const targetDevice = devices.find(d => d.id === deviceId);
        
        if (targetDevice) {
            return await biShare.connectDevice(deviceId);
        } else {
            throw new Error('设备未找到');
        }
    }
    
    // 异步：录制操作
    async recordScreen(duration = 10000) {
        try {
            await biShare.startScreenRecord({
                session: 1,
                displayId: 0,
                direction: 0
            });
            
            // 等待指定时间
            await new Promise(resolve => setTimeout(resolve, duration));
            
            return await biShare.stopScreenRecord({
                session: 1,
                displayId: 0,
                direction: 0
            });
        } catch (error) {
            console.error('录制失败:', error);
            throw error;
        }
    }
}
```

## 📊 **性能对比**

| 操作类型 | 同步模式 | 异步模式 | 推荐 |
|----------|----------|----------|------|
| **初始化** | 快速返回 | 额外开销 | 同步 ✅ |
| **设备发现** | 可能阻塞UI | 不阻塞UI | 异步 ✅ |
| **获取配置** | 快速返回 | 额外开销 | 同步 ✅ |
| **录制操作** | 阻塞UI | 不阻塞UI | 异步 ✅ |
| **文件操作** | 可能阻塞 | 不阻塞UI | 异步 ✅ |

## 🎉 **总结**

BiShare的同步/异步双模式支持让您可以：

1. **灵活选择** - 根据操作特性选择最适合的模式
2. **向后兼容** - 现有同步代码无需修改
3. **性能优化** - 耗时操作不阻塞UI线程
4. **开发便利** - 支持现代Promise和传统回调两种风格

选择合适的模式，让您的应用既高效又响应迅速！
