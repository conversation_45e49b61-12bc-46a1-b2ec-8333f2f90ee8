# BiShare框架使用指南

## 🚀 快速开始

### 1. 环境准备

#### OpenHarmony开发环境
```bash
# 确保已安装OpenHarmony SDK
# DevEco Studio 4.0+
# OpenHarmony API Level 10+
```

#### 项目集成
```typescript
// 在module.json5中添加权限
{
  "requestPermissions": [
    {
      "name": "ohos.permission.INTERNET",
      "reason": "网络通信需要"
    },
    {
      "name": "ohos.permission.CAPTURE_SCREEN", 
      "reason": "屏幕捕获需要"
    }
  ]
}
```

### 2. 基础使用

#### 初始化框架
```typescript
import bishare from 'libbishare_napi.so';

// 异步初始化
async function initBiShare() {
  try {
    const result = await bishare.initialize(
      true,           // isConsole: 是否输出控制台日志
      true,           // isFile: 是否输出文件日志
      "/data/logs",   // logPath: 日志文件路径
      3               // priority: 日志级别 (LOG_INFO)
    );
    
    if (result.success) {
      console.log('BiShare初始化成功');
    } else {
      console.error('初始化失败:', result.message);
    }
  } catch (error) {
    console.error('初始化异常:', error);
  }
}

// 回调方式初始化
bishare.initialize(true, true, "/data/logs", 3, (error, result) => {
  if (error) {
    console.error('初始化失败:', error);
  } else {
    console.log('初始化成功:', result);
  }
});
```

#### 释放资源
```typescript
async function releaseBiShare() {
  try {
    const result = await bishare.release();
    console.log('资源释放:', result.success ? '成功' : '失败');
  } catch (error) {
    console.error('释放异常:', error);
  }
}
```

## 📱 设备管理

### 1. 设备发现
```typescript
// 开始设备发现
async function discoverDevices() {
  try {
    const result = await bishare.discoverDevices();
    if (result.success) {
      console.log('设备发现已启动');
    }
  } catch (error) {
    console.error('设备发现失败:', error);
  }
}

// 获取已发现的设备
async function getDiscoveredDevices() {
  try {
    const result = await bishare.getDiscoveredDevices();
    if (result.success && result.devices) {
      result.devices.forEach(device => {
        console.log(`设备: ${device.name}, IP: ${device.ip}`);
      });
    }
  } catch (error) {
    console.error('获取设备列表失败:', error);
  }
}
```

### 2. 设备信息管理
```typescript
// 设置设备型号
async function setDeviceModel(model: string) {
  try {
    const result = await bishare.setDeviceModel(model);
    console.log('设备型号设置:', result.success ? '成功' : '失败');
  } catch (error) {
    console.error('设置设备型号失败:', error);
  }
}

// 获取设备型号
async function getDeviceModel() {
  try {
    const result = await bishare.getDeviceModel();
    if (result.success) {
      console.log('当前设备型号:', result.model);
    }
  } catch (error) {
    console.error('获取设备型号失败:', error);
  }
}

// 设置设备信息
async function setDeviceInfo(deviceInfo: any) {
  try {
    const result = await bishare.setDeviceInfo(
      deviceInfo.name,
      deviceInfo.type,
      deviceInfo.version
    );
    console.log('设备信息设置:', result.success ? '成功' : '失败');
  } catch (error) {
    console.error('设置设备信息失败:', error);
  }
}
```

## 🎥 屏幕录制与捕获

### 1. 屏幕截图
```typescript
async function takeScreenshot() {
  try {
    const result = await bishare.screenshot();
    if (result.success) {
      console.log('截图成功, 路径:', result.filePath);
      // 可以获取截图数据进行处理
      if (result.imageData) {
        // 处理图像数据
        processImageData(result.imageData);
      }
    }
  } catch (error) {
    console.error('截图失败:', error);
  }
}
```

### 2. 屏幕录制
```typescript
// 开始录制
async function startScreenRecord() {
  try {
    const result = await bishare.startScreenRecord(
      "/data/videos/record.mp4",  // 输出文件路径
      1920,                       // 宽度
      1080,                       // 高度
      30                          // 帧率
    );
    
    if (result.success) {
      console.log('录制已开始');
    }
  } catch (error) {
    console.error('开始录制失败:', error);
  }
}

// 停止录制
async function stopScreenRecord() {
  try {
    const result = await bishare.stopScreenRecord();
    if (result.success) {
      console.log('录制已停止, 文件:', result.filePath);
    }
  } catch (error) {
    console.error('停止录制失败:', error);
  }
}
```

### 3. 屏幕捕获
```typescript
async function startCapture() {
  try {
    const result = await bishare.startCapture(
      1920,    // 宽度
      1080,    // 高度
      30       // 帧率
    );
    
    if (result.success) {
      console.log('屏幕捕获已启动');
    }
  } catch (error) {
    console.error('启动捕获失败:', error);
  }
}
```

## 📡 事件监听

### 1. 事件监听器
```typescript
// 监听设备事件
bishare.on('deviceDiscovered', (deviceInfo) => {
  console.log('发现新设备:', deviceInfo);
});

bishare.on('deviceConnected', (deviceInfo) => {
  console.log('设备已连接:', deviceInfo);
});

bishare.on('deviceDisconnected', (deviceInfo) => {
  console.log('设备已断开:', deviceInfo);
});

// 监听录制事件
bishare.on('recordingStarted', (info) => {
  console.log('录制开始:', info);
});

bishare.on('recordingStopped', (info) => {
  console.log('录制结束:', info);
});

// 监听错误事件
bishare.on('error', (error) => {
  console.error('框架错误:', error);
});
```

### 2. 一次性监听
```typescript
// 只监听一次事件
bishare.once('deviceDiscovered', (deviceInfo) => {
  console.log('首次发现设备:', deviceInfo);
});
```

### 3. 移除监听器
```typescript
// 移除特定监听器
function deviceHandler(deviceInfo) {
  console.log('设备事件:', deviceInfo);
}

bishare.on('deviceDiscovered', deviceHandler);
// 稍后移除
bishare.off('deviceDiscovered', deviceHandler);

// 移除所有监听器
bishare.removeAllListeners('deviceDiscovered');
```

## 🔧 高级用法

### 1. 自定义配置
```typescript
// 设置音频输出设备
async function setAudioDevice(deviceId: string) {
  try {
    const result = await bishare.setDefaultAudioOutputDevice(deviceId);
    console.log('音频设备设置:', result.success ? '成功' : '失败');
  } catch (error) {
    console.error('设置音频设备失败:', error);
  }
}

// 设置显示尺寸
async function setDisplaySize(width: number, height: number) {
  try {
    const result = await bishare.setSize(width, height);
    console.log('显示尺寸设置:', result.success ? '成功' : '失败');
  } catch (error) {
    console.error('设置显示尺寸失败:', error);
  }
}
```

### 2. 文件操作
```typescript
// 获取根路径
async function getRootPath() {
  try {
    const result = await bishare.getRootPath();
    if (result.success) {
      console.log('根路径:', result.path);
    }
  } catch (error) {
    console.error('获取根路径失败:', error);
  }
}

// 获取当前目录
async function getCurrentDirectory() {
  try {
    const result = await bishare.getCurrentDirector();
    if (result.success) {
      console.log('当前目录:', result.directory);
    }
  } catch (error) {
    console.error('获取当前目录失败:', error);
  }
}
```

## ⚠️ 错误处理

### 1. 错误码说明
```typescript
const ErrorCodes = {
  BS_OK: 0,                    // 成功
  BS_ERROR: -1,                // 一般错误
  BS_NOT_INIT: -2,             // 未初始化
  BS_INVALID_PARAM: -3,        // 无效参数
  BS_NOT_SUPPORTED: -4,        // 不支持的操作
  BS_PERMISSION_DENIED: -5,    // 权限拒绝
  BS_DEVICE_NOT_FOUND: -6,     // 设备未找到
  BS_CONNECTION_FAILED: -7,    // 连接失败
  BS_TIMEOUT: -8,              // 超时
  BS_INSUFFICIENT_MEMORY: -9   // 内存不足
};
```

### 2. 统一错误处理
```typescript
async function handleBiShareOperation(operation: () => Promise<any>) {
  try {
    const result = await operation();
    
    if (!result.success) {
      switch (result.code) {
        case ErrorCodes.BS_NOT_INIT:
          console.error('BiShare未初始化，请先调用initialize()');
          break;
        case ErrorCodes.BS_PERMISSION_DENIED:
          console.error('权限不足，请检查应用权限设置');
          break;
        case ErrorCodes.BS_DEVICE_NOT_FOUND:
          console.error('设备未找到，请检查设备连接');
          break;
        default:
          console.error('操作失败:', result.message);
      }
    }
    
    return result;
  } catch (error) {
    console.error('操作异常:', error);
    throw error;
  }
}

// 使用示例
await handleBiShareOperation(() => bishare.discoverDevices());
```

## 🎯 最佳实践

### 1. 生命周期管理
```typescript
class BiShareManager {
  private initialized = false;
  
  async init() {
    if (!this.initialized) {
      await bishare.initialize(true, true, "/data/logs", 3);
      this.initialized = true;
      this.setupEventListeners();
    }
  }
  
  async destroy() {
    if (this.initialized) {
      this.removeEventListeners();
      await bishare.release();
      this.initialized = false;
    }
  }
  
  private setupEventListeners() {
    bishare.on('error', this.handleError.bind(this));
    bishare.on('deviceDiscovered', this.handleDeviceDiscovered.bind(this));
  }
  
  private removeEventListeners() {
    bishare.removeAllListeners();
  }
  
  private handleError(error: any) {
    console.error('BiShare错误:', error);
  }
  
  private handleDeviceDiscovered(device: any) {
    console.log('发现设备:', device);
  }
}
```

### 2. 性能优化
```typescript
// 使用防抖避免频繁调用
function debounce(func: Function, wait: number) {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

const debouncedDiscoverDevices = debounce(bishare.discoverDevices, 1000);

// 缓存设备列表
class DeviceCache {
  private devices: any[] = [];
  private lastUpdate = 0;
  private cacheTimeout = 5000; // 5秒缓存
  
  async getDevices() {
    const now = Date.now();
    if (now - this.lastUpdate > this.cacheTimeout) {
      const result = await bishare.getDiscoveredDevices();
      if (result.success) {
        this.devices = result.devices;
        this.lastUpdate = now;
      }
    }
    return this.devices;
  }
}
```
