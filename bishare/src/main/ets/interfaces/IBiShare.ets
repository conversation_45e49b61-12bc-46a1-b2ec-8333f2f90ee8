import {
  EventType, EventCallback,
  Callback, InitOptions, RecordingOptions, ScreenshotOptions,
  SizeOptions, DeviceInfoOptions, DeviceInfo,
  NetworkInfoOptions } from './BiShareTypes';

/**
 * BiShare Interface Definition
 */
export default interface IBiShare {
  /**
   * Initialize BiShare service
   * @param options Initialization options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  initialize(options: InitOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Release BiShare service
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  release(callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Discover devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Clear discovered devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  clearDiscoveredDevices(callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Get discovered devices
   * @param callback Optional callback function
   * @returns Promise<DeviceInfo[]> if no callback is provided
   */
  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void;

  /**
   * Start screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Stop screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  stopScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Start capture
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startCapture(callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Set device information
   * @param options Device information options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceInfo(options: DeviceInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Set device model
   * @param model Device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceModel(model: string, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Get device model
   * @returns Device model
   */
  getDeviceModel(): string;

  /**
   * Reset device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  resetDeviceModel(callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Get root path
   * @returns Root path
   */
  getRootPath(): string;

  /**
   * Get current directory
   * @returns Current directory
   */
  getCurrentDirector(): string;

  /**
   * Set screen and video size
   * @param options Size options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setSize(options: SizeOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Set default audio output device
   * @param enable Whether to enable the default audio output device
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDefaultAudioOutputDevice(enable: boolean, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Take a screenshot
   * @param options Screenshot options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  screenshot(options: ScreenshotOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Find a remote device
   * @param pincode Device PIN code
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  findRemoteDevice(pincode: string, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * 设置本机网络参数
   * @param options 网络信息
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void;

  /**
   * Register an event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  on(eventType: EventType, callback: EventCallback): void;

  /**
   * Unregister an event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  off(eventType: EventType, callback: EventCallback): void;

  /**
   * Register a one-time event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  once(eventType: EventType, callback: EventCallback): void;
}