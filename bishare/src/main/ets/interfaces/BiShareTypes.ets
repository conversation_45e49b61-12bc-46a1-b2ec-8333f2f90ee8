/**
 * BiShare type definitions
 */

/**
 * Blog priority levels
 */
export enum BlogPriority {
  EMERG = 0,
  FATAL = 1,
  ALERT = 2,
  CRIT = 3,
  ERROR = 4,
  WARN = 5,
  NOTICE = 6,
  INFO = 7,
  DEBUG = 8
}

/**
 * Event types
 */
export enum EventType {
  DEVICE_INFO = 1,
  DEVICE_STATUS = 2,
  CONNECT_STATUS = 3,
  KEY_VALUE = 4,
  ERROR_INFO = 5,
  MEDIA_CENTER = 6,
  SCREEN_RECORD = 7,
  FILE_ACTION = 8,
  WFD_EVENT = 9,
  ABILITY_ACTION = 10,
  DEVICE_INFO_LIST = 11,
  WINDOWS_INFO_LIST = 12,
  MONITORS_INFO_LIST = 13,
  LOG_INFO = 99
}

/**
 * Recording direction
 */
export enum Direction {
  NULL = 0,
  SEND = 1,
  RECV = 2
}

/**
 * Buffer types
 */
export enum BufferType {
  VIDEO = 1,
  AUDIO = 2
}

/**
 * BiShare status codes
 */
export enum BiShareStatus {
  OK = 0,
  NO_ERROR = 0,
  NOT_INIT = 1,
  NOT_FOUND = 2,
  PARAMS_ERROR = 3,
  LIBRARY_ERROR = 4,
  OPS_ERROR = 5,
  RELEASE_ERROR = 6,
  TIMEOUT = 7,
  ALLOCATE_ERROR = 8,
  OPEN_ERROR = 9,
  SEND_ERROR = 10,
  RECV_ERROR = 11
}

/**
 * 设备认证信息（与NAPI层保持一致）
 */
export interface DeviceAuthInfo {
  name: string;
  password: string;
}

/**
 * 设备详细信息（业务层使用）
 */
export interface DeviceDetailInfo {
  id: string;
  name: string;
  address: string;
  model: string;
  pincode: string;
  status: number;
  connected: boolean;
}

/**
 * 设备信息（业务层主要使用DeviceDetailInfo，此接口保留用于特殊场景）
 */
export interface DeviceInfo extends DeviceDetailInfo {
  // 业务层设备信息，包含完整的设备属性
}

/**
 * Event data interface
 */
export interface EventData {
  type: EventType;
  data: string;
}

/**
 * Packet data interface
 */
export interface PacketData {
  session: number;
  bufferType: BufferType;
  buffer: ArrayBuffer;
  width: number;
  height: number;
  timestamp: number;
}

/**
 * Initialization options
 */
export interface InitOptions {
  isConsole: boolean;
  isFile: boolean;
  filePath: string;
  priority?: BlogPriority;
}

/**
 * Recording options
 */
export interface RecordingOptions {
  session: number;
  displayId: number;
  direction: Direction;
}

/**
 * Screenshot options
 */
export interface ScreenshotOptions {
  filePath: string;
  top: number;
  bottom: number;
  left: number;
  right: number;
}

/**
 * Size options
 */
export interface SizeOptions {
  screenWidth: number;
  screenHeight: number;
  videoWidth: number;
  videoHeight: number;
}

/**
 * Device information options (设备认证选项，与NAPI层一致)
 */
export interface DeviceInfoOptions extends DeviceAuthInfo {
  // 继承name和password
}

/**
 * 网络类型（与NAPI层保持一致）
 */
export enum NetworkType {
  Ethernet = 0,
  Wlan = 1,
  Mobile = 2
}

/**
 * 设置本机网络参数
 */
export interface NetworkInfoOptions {
  networkType: NetworkType;
  /**
   * 本机IP
   */
  addr: string;
  /**
   * 本机MAC
   */
  mac: string;
}

/**
 * Event callback type
 */
export type EventCallback = (data: EventData) => void;

/**
 * Standard callback type
 */
export type Callback<T> = (error: Error | null, result: T) => void;

// ============================================================================
// 扩展类型定义（原entry模块内容）
// ============================================================================

/**
 * BiShare错误类型
 */
export interface BiShareError {
  code: string;
  message: string;
  details?: Record<string, Object>;
}

/**
 * BiShare统一返回结果
 */
export interface BiShareResult<T> {
  success: boolean;
  data?: T;
  error?: BiShareError;
}

/**
 * 设备连接状态
 */
export enum DeviceConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  FAILED = 'failed'
}

/**
 * 扩展的设备信息（包含认证和详细信息）
 */
export interface ExtendedDeviceInfo extends DeviceDetailInfo {
  /** 设备认证密码 */
  password?: string;
  /** 连接状态 */
  connectionStatus: DeviceConnectionStatus;
  /** 最后连接时间 */
  lastConnectedTime?: number;
  /** 连接次数 */
  connectionCount?: number;
}

/**
 * 录制状态
 */
export enum RecordingStatus {
  IDLE = 'idle',
  RECORDING = 'recording',
  PAUSED = 'paused',
  STOPPED = 'stopped'
}

/**
 * 录制信息
 */
export interface RecordingInfo {
  status: RecordingStatus;
  filePath?: string;
  duration?: number;
  startTime?: number;
}

/**
 * 网络状态
 */
export enum NetworkStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected'
}

/**
 * 网络信息
 */
export interface NetworkInfo {
  status: NetworkStatus;
  ssid?: string;
  ipAddress?: string;
  macAddress?: string;
}

/**
 * 事件类型枚举（扩展版本）
 */
export enum BiShareEventType {
  // 设备事件
  DEVICE_DISCOVERED = 'device_discovered',
  DEVICE_CONNECTED = 'device_connected',
  DEVICE_DISCONNECTED = 'device_disconnected',
  DEVICE_UPDATED = 'device_updated',

  // 录制事件
  RECORDING_STARTED = 'recording_started',
  RECORDING_STOPPED = 'recording_stopped',
  RECORDING_PAUSED = 'recording_paused',
  RECORDING_RESUMED = 'recording_resumed',

  // 网络事件
  NETWORK_CONNECTED = 'network_connected',
  NETWORK_DISCONNECTED = 'network_disconnected',

  // 文件事件
  FILE_RECEIVED = 'file_received',
  FILE_SENT = 'file_sent',

  // 消息事件
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_SENT = 'message_sent'
}

/**
 * 事件数据
 */
export interface BiShareEventData {
  type: BiShareEventType;
  timestamp: number;
  data: Record<string, Object>;
}

/**
 * 事件监听器基类
 */
export class BiShareEventListener {
  onEvent(eventData: BiShareEventData): void {
    // 默认实现
  }
}

/**
 * 设备事件监听器
 */
export class DeviceEventListener {
  onDeviceDiscovered(device: ExtendedDeviceInfo): void {
    // 默认实现
  }

  onDeviceConnected(device: ExtendedDeviceInfo): void {
    // 默认实现
  }

  onDeviceDisconnected(device: ExtendedDeviceInfo): void {
    // 默认实现
  }

  onDeviceUpdated(oldDevice: ExtendedDeviceInfo, newDevice: ExtendedDeviceInfo): void {
    // 默认实现
  }
}

/**
 * 录制事件监听器
 */
export class RecordingEventListener {
  onRecordingStarted(info: RecordingInfo): void {
    // 默认实现
  }

  onRecordingStopped(info: RecordingInfo): void {
    // 默认实现
  }

  onRecordingPaused(info: RecordingInfo): void {
    // 默认实现
  }

  onRecordingResumed(info: RecordingInfo): void {
    // 默认实现
  }
}

/**
 * 网络事件监听器
 */
export class NetworkEventListener {
  onNetworkConnected(info: NetworkInfo): void {
    // 默认实现
  }

  onNetworkDisconnected(info: NetworkInfo): void {
    // 默认实现
  }
}

/**
 * 文件传输事件监听器
 */
export class FileEventListener {
  onFileReceived(filePath: string, fromDevice: string): void {
    // 默认实现
  }

  onFileSent(filePath: string, toDevice: string): void {
    // 默认实现
  }
}

/**
 * 消息事件监听器
 */
export class MessageEventListener {
  onMessageReceived(message: string, fromDevice: string): void {
    // 默认实现
  }

  onMessageSent(message: string, toDevice: string): void {
    // 默认实现
  }
}