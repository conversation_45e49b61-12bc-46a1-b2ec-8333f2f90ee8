import hilog from '@ohos.hilog';


/**
 * 定义日志参数类型
 */
type LogParam = string | number | boolean | object;

/**
 * Logger class for BiShare
 */
export class Logger {
  private domain: number;
  private prefix: string;
  private format: string;

  /**
   * Constructor
   * @param prefix Log prefix
   */
  constructor(prefix: string) {
    this.domain = 0xFF00; // Example domain ID
    this.prefix = prefix;
    this.format = `%{public}s`;
  }

  debug(...msg: LogParam[]) {
    hilog.debug(this.domain, this.prefix, this.format, msg.join(' '));
  }

  info(...msg: LogParam[]) {
    hilog.info(this.domain, this.prefix, this.format, msg.join(' '));
  }

  /**
   * Warn log
   * @param msg Log message
   */
  warn(...msg: LogParam[]) {
    hilog.warn(this.domain, this.prefix, this.format, msg.join(' '));
  }

  /**
   * Error log
   * @param msg Log message
   */
  error(...msg: LogParam[]) {
    hilog.error(this.domain, this.prefix, this.format, msg.join(' '));
  }

  /**
   * Fatal log
   * @param msg Log message
   */
  fatal(...msg: LogParam[]) {
    hilog.fatal(this.domain, this.prefix, this.format, msg.join(' '));
  }
}

/**
 * Create a logger instance
 * @param prefix Log prefix
 * @returns Logger instance
 */
export default function createLogger(prefix: string): Logger {
  return new Logger(prefix);
}