import createLogger from '../utils/Logger';
import { RecordingOptions, ScreenshotOptions, SizeOptions, Callback } from '../interfaces/BiShareTypes';
import { screenshot, setDefaultAudioOutputDevice, setSize, startCapture,
  startScreenRecord,
  stopScreenRecord } from 'libbishare_napi.so';

const logger = createLogger('BiShare.RecordingManager');

/**
 * Recording Manager class - handles screen recording operations
 */
export default class RecordingManager {
  private static instance: RecordingManager;
  private isRecording: boolean = false;

  /**
   * Private constructor to implement Singleton pattern
   */
  private constructor() {
    logger.info('RecordingManager created');
  }

  /**
   * Get RecordingManager instance (Singleton)
   * @returns RecordingManager instance
   */
  static getInstance(): RecordingManager {
    if (!RecordingManager.instance) {
      RecordingManager.instance = new RecordingManager();
    }
    return RecordingManager.instance;
  }

  /**
   * Start screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      startScreenRecord(
        options.session,
        options.displayId,
        options.direction,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to start screen recording with callback: ${error.message}`);
          } else {
            this.isRecording = true;
            logger.info(`Started screen recording with callback: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
          }
          callback(error, result);
        }
      );
    } else {
      return new Promise<boolean>((resolve, reject) => {
        startScreenRecord(
          options.session,
          options.displayId,
          options.direction,
          (error: Error | null, result: boolean) => {
            if (error) {
              logger.error(`Failed to start screen recording: ${error.message}`);
              reject(error);
            } else {
              this.isRecording = true;
              logger.info(`Started screen recording: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
              resolve(result);
            }
          }
        );
      });
    }
  }

  /**
   * Stop screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  stopScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      stopScreenRecord(
        options.session,
        options.displayId,
        options.direction,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to stop screen recording with callback: ${error.message}`);
          } else {
            this.isRecording = false;
            logger.info(`Stopped screen recording with callback: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
          }
          callback(error, result);
        }
      );
    } else {
      return new Promise<boolean>((resolve, reject) => {
        stopScreenRecord(
          options.session,
          options.displayId,
          options.direction,
          (error: Error | null, result: boolean) => {
            if (error) {
              logger.error(`Failed to stop screen recording: ${error.message}`);
              reject(error);
            } else {
              this.isRecording = false;
              logger.info(`Stopped screen recording: session=${options.session}, displayId=${options.displayId}, direction=${options.direction}`);
              resolve(result);
            }
          }
        );
      });
    }
  }

  /**
   * Start capture
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startCapture(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      startCapture((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to start capture with callback: ${error.message}`);
        } else {
          logger.info('Started capture with callback');
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        startCapture((error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to start capture: ${error.message}`);
            reject(error);
          } else {
            logger.info('Started capture');
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Set screen and video size
   * @param options Size options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setSize(options: SizeOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      setSize(
        options.screenWidth,
        options.screenHeight,
        options.videoWidth,
        options.videoHeight,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to set size with callback: ${error.message}`);
          } else {
            logger.info(`Set size with callback: screenWidth=${options.screenWidth}, screenHeight=${options.screenHeight}, videoWidth=${options.videoWidth}, videoHeight=${options.videoHeight}`);
          }
          callback(error, result);
        }
      );
    } else {
      return new Promise<boolean>((resolve, reject) => {
        setSize(
          options.screenWidth,
          options.screenHeight,
          options.videoWidth,
          options.videoHeight,
          (error: Error | null, result: boolean) => {
            if (error) {
              logger.error(`Failed to set size: ${error.message}`);
              reject(error);
            } else {
              logger.info(`Set size: screenWidth=${options.screenWidth}, screenHeight=${options.screenHeight}, videoWidth=${options.videoWidth}, videoHeight=${options.videoHeight}`);
              resolve(result);
            }
          }
        );
      });
    }
  }

  /**
   * Set default audio output device
   * @param enable Whether to enable the default audio output device
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDefaultAudioOutputDevice(enable: boolean, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      setDefaultAudioOutputDevice(enable, (error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to set default audio output device with callback: ${error.message}`);
        } else {
          logger.info(`Set default audio output device with callback: enable=${enable}`);
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        setDefaultAudioOutputDevice(enable, (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to set default audio output device: ${error.message}`);
            reject(error);
          } else {
            logger.info(`Set default audio output device: enable=${enable}`);
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Take a screenshot
   * @param options Screenshot options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  screenshot(options: ScreenshotOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      screenshot(
        options.filePath,
        options.top,
        options.bottom,
        options.left,
        options.right,
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to take screenshot with callback: ${error.message}`);
          } else {
            logger.info(`Took screenshot with callback: filePath=${options.filePath}`);
          }
          callback(error, result);
        }
      );
    } else {
      return new Promise<boolean>((resolve, reject) => {
        screenshot(
          options.filePath,
          options.top,
          options.bottom,
          options.left,
          options.right,
          (error: Error | null, result: boolean) => {
            if (error) {
              logger.error(`Failed to take screenshot: ${error.message}`);
              reject(error);
            } else {
              logger.info(`Took screenshot: filePath=${options.filePath}`);
              resolve(result);
            }
          }
        );
      });
    }
  }

  /**
   * Check if recording is in progress
   * @returns True if recording is in progress, false otherwise
   */
  isRecordingInProgress(): boolean {
    return this.isRecording;
  }
}