import createLogger from '../utils/Logger';
import { EventType, EventCallback, EventData } from '../interfaces/BiShareTypes';
import { off, on } from 'libbishare_napi.so';

const logger = createLogger('BiShare.EventManager');

/**
 * Event Manager class - handles event registration and dispatching
 */
export default class EventManager {
  private static instance: EventManager;
  private callbacks: Map<EventType, Set<EventCallback>>;

  /**
   * Private constructor to implement Singleton pattern
   */
  private constructor() {
    this.callbacks = new Map<EventType, Set<EventCallback>>();
    logger.info('EventManager created');
  }

  /**
   * Get EventManager instance (Singleton)
   * @returns EventManager instance
   */
  static getInstance(): EventManager {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
    }
    return EventManager.instance;
  }

  /**
   * Register an event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  on(eventType: EventType, callback: EventCallback): void {
    if (!this.callbacks.has(eventType)) {
      this.callbacks.set(eventType, new Set<EventCallback>());
    }

    const callbackSet = this.callbacks.get(eventType) as Set<EventCallback>;
    callbackSet.add(callback);

    // Register with the native module
    try {
      on(eventType, (data: EventData) => {
        this.dispatchEvent(eventType, data);
      });
      logger.info(`Registered event listener for type: ${eventType}`);
    } catch (error) {
      logger.error(`Failed to register event listener for type ${eventType}: ${error}`);
      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error(`Failed to register event listener: ${String(error)}`);
      }
    }
  }

  /**
   * Unregister an event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  off(eventType: EventType, callback: EventCallback): void {
    const callbackSet = this.callbacks.get(eventType);
    if (callbackSet && callbackSet.has(callback)) {
      callbackSet.delete(callback);

      // If no callbacks are left for this type, unregister from native module
      if (callbackSet.size === 0) {
        this.callbacks.delete(eventType);
        try {
          off(eventType, () => {});
          logger.info(`Unregistered event listener for type: ${eventType}`);
        } catch (error) {
          logger.error(`Failed to unregister event listener for type ${eventType}: ${error}`);
          if (error instanceof Error) {
            throw error;
          } else {
            throw new Error(`Failed to unregister event listener: ${String(error)}`);
          }
        }
      }
    }
  }

  /**
   * Register a one-time event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  once(eventType: EventType, callback: EventCallback): void {
    // Create a wrapper function that will call the callback and unregister itself
    const wrappedCallback: EventCallback = (data: EventData) => {
      // Call the original callback
      callback(data);

      // Unregister the event listener
      this.off(eventType, wrappedCallback);
    };

    // Register the wrapper function
    this.on(eventType, wrappedCallback);
    logger.info(`Registered one-time event listener for type: ${eventType}`);
  }

  /**
   * Dispatch an event to all registered callbacks
   * @param eventType Event type
   * @param data Event data
   */
  private dispatchEvent(eventType: EventType, data: EventData): void {
    const callbackSet = this.callbacks.get(eventType);
    if (callbackSet) {
      callbackSet.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          logger.error(`Error in event callback for type ${eventType}: ${error}`);
        }
      });
    }
  }
}