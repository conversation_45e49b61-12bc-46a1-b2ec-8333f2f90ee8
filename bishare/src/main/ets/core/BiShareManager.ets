import createLogger from '../utils/Logger';
import DeviceManager from './DeviceManager';
import RecordingManager from './RecordingManager';
import EventManager from './EventManager';
import IBiShare from '../interfaces/IBiShare';
import {
  InitOptions, RecordingOptions, ScreenshotOptions, SizeOptions,
  DeviceInfoOptions, DeviceInfo, EventType, EventCallback,
  Callback,
  NetworkInfoOptions
} from '../interfaces/BiShareTypes';
import { initialize, release } from 'libbishare_napi.so';


const logger = createLogger('BiShare.BiShareManager');

/**
 * BiShare Manager - main entry point for the framework
 * Implements the IBiShare interface
 */
export default class BiShareManager implements IBiShare {
  private static instance: BiShareManager;
  private deviceManager: DeviceManager;
  private recordingManager: RecordingManager;
  private eventManager: EventManager;
  private initialized: boolean = false;

  /**
   * Private constructor to implement Singleton pattern
   */
  private constructor() {
    // Get manager instances
    this.deviceManager = DeviceManager.getInstance();
    this.recordingManager = RecordingManager.getInstance();
    this.eventManager = EventManager.getInstance();

    logger.info('BiShareManager created');
  }

  /**
   * Get BiShareManager instance (Singleton)
   * @returns BiShareManager instance
   */
  static getInstance(): BiShareManager {
    if (!BiShareManager.instance) {
      BiShareManager.instance = new BiShareManager();
    }
    return BiShareManager.instance;
  }

  /**
   * Initialize BiShare service
   * @param options Initialization options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  initialize(options: InitOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      initialize(
        options.isConsole,
        options.isFile,
        options.filePath,
        options.priority || 7, // Default to INFO
        (error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to initialize BiShare with callback: ${error.message}`);
          } else {
            this.initialized = true;
            logger.info('BiShare initialized successfully with callback');
          }
          callback(error, result);
        }
      );
    } else {
      return new Promise<boolean>((resolve, reject) => {
        initialize(
          options.isConsole,
          options.isFile,
          options.filePath,
          options.priority || 7, // Default to INFO
          (error: Error | null, result: boolean) => {
            if (error) {
              logger.error(`Failed to initialize BiShare: ${error.message}`);
              reject(error);
            } else {
              this.initialized = true;
              logger.info('BiShare initialized successfully');
              resolve(result);
            }
          }
        );
      });
    }
  }

  /**
   * Release BiShare service
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  release(callback?: Callback<boolean>): Promise<boolean> | void {
    if (callback) {
      release((error: Error | null, result: boolean) => {
        if (error) {
          logger.error(`Failed to release BiShare with callback: ${error.message}`);
        } else {
          this.initialized = false;
          logger.info('BiShare released successfully with callback');
        }
        callback(error, result);
      });
    } else {
      return new Promise<boolean>((resolve, reject) => {
        release((error: Error | null, result: boolean) => {
          if (error) {
            logger.error(`Failed to release BiShare: ${error.message}`);
            reject(error);
          } else {
            this.initialized = false;
            logger.info('BiShare released successfully');
            resolve(result);
          }
        });
      });
    }
  }

  /**
   * Discover devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  discoverDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.discoverDevices(callback);
  }

  /**
   * Clear discovered devices
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  clearDiscoveredDevices(callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.clearDiscoveredDevices(callback);
  }

  /**
   * Get discovered devices
   * @param callback Optional callback function
   * @returns Promise<DeviceInfo[]> if no callback is provided
   */
  getDiscoveredDevices(callback?: Callback<DeviceInfo[]>): Promise<DeviceInfo[]> | void {
    this.checkInitialization();
    return this.deviceManager.getDiscoveredDevices(callback);
  }

  /**
   * Start screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.recordingManager.startScreenRecord(options, callback);
  }

  /**
   * Stop screen recording
   * @param options Recording options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  stopScreenRecord(options: RecordingOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.recordingManager.stopScreenRecord(options, callback);
  }

  /**
   * Start capture
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  startCapture(callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.recordingManager.startCapture(callback);
  }

  /**
   * Set device information
   * @param options Device information options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceInfo(options: DeviceInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.setDeviceInfo(options, callback);
  }

  /**
   * Set device model
   * @param model Device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDeviceModel(model: string, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.setDeviceModel(model, callback);
  }

  /**
   * Get device model
   * @returns Device model
   */
  getDeviceModel(): string {
    this.checkInitialization();
    return this.deviceManager.getDeviceModel();
  }

  /**
   * Reset device model
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  resetDeviceModel(callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.resetDeviceModel(callback);
  }

  /**
   * Get root path
   * @returns Root path
   */
  getRootPath(): string {
    this.checkInitialization();
    return this.deviceManager.getRootPath();
  }

  /**
   * Get current directory
   * @returns Current directory
   */
  getCurrentDirector(): string {
    this.checkInitialization();
    return this.deviceManager.getCurrentDirector();
  }

  /**
   * Set screen and video size
   * @param options Size options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setSize(options: SizeOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.recordingManager.setSize(options, callback);
  }

  /**
   * Set default audio output device
   * @param enable Whether to enable the default audio output device
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setDefaultAudioOutputDevice(enable: boolean, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.recordingManager.setDefaultAudioOutputDevice(enable, callback);
  }

  /**
   * Take a screenshot
   * @param options Screenshot options
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  screenshot(options: ScreenshotOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.recordingManager.screenshot(options, callback);
  }

  /**
   * Find a remote device
   * @param pincode Device PIN code
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  findRemoteDevice(pincode: string, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.findRemoteDevice(pincode, callback);
  }

  /**
   * 设置本机网络参数
   * @param options 网络信息
   * @param callback Optional callback function
   * @returns Promise<boolean> if no callback is provided
   */
  setNetworkInfo(options: NetworkInfoOptions, callback?: Callback<boolean>): Promise<boolean> | void {
    this.checkInitialization();
    return this.deviceManager.setNetworkInfo(options, callback);
  }

  /**
   * Register an event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  on(eventType: EventType, callback: EventCallback): void {
    this.checkInitialization();
    this.eventManager.on(eventType, callback);
  }

  /**
   * Unregister an event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  off(eventType: EventType, callback: EventCallback): void {
    this.checkInitialization();
    this.eventManager.off(eventType, callback);
  }

  /**
   * Register a one-time event listener
   * @param eventType Event type
   * @param callback Callback function
   */
  once(eventType: EventType, callback: EventCallback): void {
    this.checkInitialization();
    this.eventManager.once(eventType, callback);
  }

  /**
   * Check if BiShare is initialized
   * @throws Error if BiShare is not initialized
   */
  private checkInitialization(): void {
    if (!this.initialized) {
      const error = new Error('BiShare is not initialized');
      logger.error(error.message);
      throw error;
    }
  }
}