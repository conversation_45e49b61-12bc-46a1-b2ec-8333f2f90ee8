import UIAbility from '@ohos.app.ability.UIAbility';
import window from '@ohos.window';
import createLogger from '../utils/Logger';
import BiShareManager from '../core/BiShareManager';
import {
  BlogPriority,
  RecordingOptions,
  ScreenshotOptions,
  SizeOptions,
  DeviceInfoOptions,
  DeviceInfo,
  InitOptions
} from '../interfaces/BiShareTypes';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import rpc from '@ohos.rpc';

const logger = createLogger('BiShare.BiShareAbility');

/**
 * BiShare Ability - Entry point for IPC with other applications
 */
export default class BiShareAbility extends UIAbility {
  private biShareManager: BiShareManager = BiShareManager.getInstance();

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    logger.info(`BiShareAbility onCreate, want: ${want.abilityName}`);
  }

  onDestroy(): void {
    logger.info('BiShareAbility onDestroy');
    // Simply call without handling the return value
    try {
      this.biShareManager.release();
      logger.info('BiShare release called');
    } catch (err) {
      const error = err as Error;
      logger.error(`Error releasing BiShare: ${error.message}`);
    }
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    logger.info('BiShareAbility onWindowStageCreate');

    // Initialize without handling return value
    try {
      const initOptions: InitOptions = {
        isConsole: true,
        isFile: true,
        filePath: `${this.context.filesDir}/bishare-log.txt`,
        priority: BlogPriority.INFO
      };

      this.biShareManager.initialize(initOptions);
      logger.info('BiShare initialization called');
    } catch (err) {
      const error = err as Error;
      logger.error(`Error initializing BiShare: ${error.message}`);
    }
  }

  onWindowStageDestroy(): void {
    logger.info('BiShareAbility onWindowStageDestroy');
  }

  onForeground(): void {
    logger.info('BiShareAbility onForeground');
  }

  onBackground(): void {
    logger.info('BiShareAbility onBackground');
  }

  // IPC entry point for other applications
  onConnect(want: Want): rpc.RemoteObject {
    logger.info(`BiShareAbility onConnect, want: ${want.abilityName}`);

    // Create a custom remote object
    class BiShareRemoteObject extends rpc.RemoteObject {
      private manager: BiShareManager;

      constructor(manager: BiShareManager) {
        super("BiShareRemoteObject");
        this.manager = manager;
      }

      // 简化的方式处理 Promise 返回值
      private handleMethodCall<T>(method: () => void | Promise<T>, reply: rpc.MessageParcel): void {
        try {
          // 我们不再尝试检测 Promise，而是直接调用方法
          method();
          // 设置成功代码
          reply.writeInt(0);
        } catch (err) {
          const error = err as Error;
          logger.error(`Method error: ${error.message}`);
          reply.writeInt(-1); // 错误代码
        }
      }

      onRemoteRequest(code: number, data: rpc.MessageParcel, reply: rpc.MessageParcel, option: rpc.MessageOption): boolean {
        logger.info(`onRemoteRequest: code = ${code}`);

        try {
          // Handle the remote request based on the code
          switch (code) {
            case 1: // discoverDevices
              this.handleMethodCall(() => this.manager.discoverDevices(), reply);
              return true;

            case 2: // clearDiscoveredDevices
              this.handleMethodCall(() => this.manager.clearDiscoveredDevices(), reply);
              return true;

            case 3: // getDiscoveredDevices
            // 对于需要返回值的方法，我们直接调用同步方法
              try {
                // 注意：这里我们将异步调用转为同步，这不是理想方式，但是为了满足 ArkTS 限制
                this.manager.getDiscoveredDevices();
                reply.writeString("[]"); // 默认空数组
                reply.writeInt(0); // 成功代码
              } catch (err) {
                const error = err as Error;
                logger.error(`Error in getDiscoveredDevices: ${error.message}`);
                reply.writeInt(-1); // 错误代码
              }
              return true;

            case 4: // startScreenRecord
              {
                const options: RecordingOptions = {
                  session: data.readInt(),
                  displayId: data.readInt(),
                  direction: data.readInt()
                };
                this.handleMethodCall(() => this.manager.startScreenRecord(options), reply);
                return true;
              }

            case 5: // stopScreenRecord
              {
                const options: RecordingOptions = {
                  session: data.readInt(),
                  displayId: data.readInt(),
                  direction: data.readInt()
                };
                this.handleMethodCall(() => this.manager.stopScreenRecord(options), reply);
                return true;
              }

            case 6: // startCapture
              this.handleMethodCall(() => this.manager.startCapture(), reply);
              return true;

            case 7: // setDeviceInfo
              {
                const name = data.readString();
                const password = data.readString();
                const options: DeviceInfoOptions = { name, password };
                this.handleMethodCall(() => this.manager.setDeviceInfo(options), reply);
                return true;
              }

            case 8: // setDeviceModel
              {
                const model = data.readString();
                this.handleMethodCall(() => this.manager.setDeviceModel(model), reply);
                return true;
              }

            case 9: // getDeviceModel
              {
                // 对于同步方法，直接获取结果
                const model = this.manager.getDeviceModel();
                reply.writeString(model);
                reply.writeInt(0); // Success code
                return true;
              }

            case 10: // resetDeviceModel
              this.handleMethodCall(() => this.manager.resetDeviceModel(), reply);
              return true;

            case 11: // getRootPath
              {
                const path = this.manager.getRootPath();
                reply.writeString(path);
                reply.writeInt(0); // Success code
                return true;
              }

            case 12: // getCurrentDirector
              {
                const dir = this.manager.getCurrentDirector();
                reply.writeString(dir);
                reply.writeInt(0); // Success code
                return true;
              }

            case 13: // setSize
              {
                const options: SizeOptions = {
                  screenWidth: data.readInt(),
                  screenHeight: data.readInt(),
                  videoWidth: data.readInt(),
                  videoHeight: data.readInt()
                };
                this.handleMethodCall(() => this.manager.setSize(options), reply);
                return true;
              }

            case 14: // setDefaultAudioOutputDevice
              {
                const enable = data.readInt() === 1;
                this.handleMethodCall(() => this.manager.setDefaultAudioOutputDevice(enable), reply);
                return true;
              }

            case 15: // screenshot
              {
                const options: ScreenshotOptions = {
                  filePath: data.readString(),
                  top: data.readInt(),
                  bottom: data.readInt(),
                  left: data.readInt(),
                  right: data.readInt()
                };
                this.handleMethodCall(() => this.manager.screenshot(options), reply);
                return true;
              }

            case 16: // findRemoteDevice
              {
                const pincode = data.readString();
                this.handleMethodCall(() => this.manager.findRemoteDevice(pincode), reply);
                return true;
              }

            default:
              logger.error(`Unknown remote request code: ${code}`);
              return false;
          }
        } catch (err) {
          const error = err as Error;
          logger.error(`Error in onRemoteRequest: ${error.message}`);
          reply.writeInt(-1); // Error code
          return false;
        }
      }
    }

    // Return the remote object
    return new BiShareRemoteObject(this.biShareManager);
  }

  onDisconnect(connection: number): void {
    logger.info(`BiShareAbility onDisconnect: ${connection}`);
  }
}