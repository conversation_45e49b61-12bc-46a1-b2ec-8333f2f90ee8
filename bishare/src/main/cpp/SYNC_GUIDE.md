# BiShare NAPI 同步指南

## 概述

BiShare NAPI模块包含两个关键文件，它们必须保持同步：

1. **C++ 实现**: `interfaces/napi/bishare_napi_interface.cpp`
2. **TypeScript 定义**: `types/libbishare_napi/index.d.ts`

## 需要同步的内容

### 1. 常量定义
- 版本信息 (`VERSION`)
- 日志优先级 (`LOG_PRIORITY_*`)
- 事件类型 (`EVENT_*`)

### 2. 函数签名
- 所有导出的NAPI函数
- 参数类型和数量
- 返回值类型

### 3. 接口定义
- `RecordingOptions`
- `ScreenshotOptions`
- `DeviceInfo`
- `EventData`

## 已实施的优化

### 删除冗余文件
- ✅ 删除了 `bishare_error_codes.d.ts` 文件（重复定义）
- ✅ 在主要的 `index.d.ts` 中统一管理所有类型定义
- ✅ 在 C++ 层正确导出错误码常量
- ✅ 避免了多个文件间的不一致问题

### 统一架构
- **C++ 实现层**: `interfaces/napi/bishare_napi_interface.cpp`
- **TypeScript 定义层**: `types/libbishare_napi/index.d.ts`
- **同步机制**: 通过注释和文档确保一致性

## 修改流程

### 添加新函数时：
1. 在 `bishare_napi_interface.cpp` 中实现C++函数
2. 在 `index.d.ts` 中添加对应的TypeScript声明
3. 确保参数类型和返回值类型匹配

### 修改现有函数时：
1. 同时修改C++实现和TypeScript声明
2. 确保参数变化在两个文件中保持一致

### 添加新常量时：
1. 在 `bishare_napi_interface.cpp` 的 `constants[]` 数组中添加
2. 在 `index.d.ts` 中添加对应的常量声明

## 检查清单

在提交代码前，请确认：

- [ ] 新增/修改的函数在两个文件中都有对应定义
- [ ] 参数类型和数量完全匹配
- [ ] 返回值类型一致
- [ ] 常量值和名称一致
- [ ] 接口定义完全匹配

## 自动化建议

未来可以考虑：
1. 使用代码生成工具从C++自动生成TypeScript定义
2. 创建CI检查脚本验证两个文件的一致性
3. 使用JSON配置文件统一管理接口定义

## 注意事项

- TypeScript的可选参数（如回调函数）在C++中通过参数数量检查实现
- Promise返回类型在C++中通过异步工作实现
- 错误处理模式需要在两端保持一致
