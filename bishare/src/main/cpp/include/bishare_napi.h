#ifndef BISHARE_NAPI_H
#define BISHARE_NAPI_H

#include <atomic>
#include <memory>
#include <map>
#include <vector>
#include <pthread.h>
#include <napi/native_api.h>

namespace OHOS {
    namespace BiShare {

        // Forward declarations
        class BiShareCallbacks;
        class BiShareDeviceManager;
        class BiShareRecordManager;

        /**
         * BiShare内部管理类
         *
         * 职责：
         * 1. 管理BiShare服务的全局状态
         * 2. 提供单例访问模式
         * 3. 管理各种业务管理器的生命周期
         *
         * 注意：NAPI绑定功能已迁移到BiShareNapiInterface
         */
        class BiShareNapi {
        public:
            /**
             * 获取单例实例
             */
            static BiShareNapi* GetInstance();

            /**
             * 获取管理器实例
             */
            std::shared_ptr<BiShareCallbacks> GetCallbacks() const { return callbacks_; }
            std::shared_ptr<BiShareDeviceManager> GetDeviceManager() const { return deviceManager_; }
            std::shared_ptr<BiShareRecordManager> GetRecordManager() const { return recordManager_; }

            /**
             * 检查初始化状态
             */
            static bool IsInitialized() { return isInitialized_.load(); }

            /**
             * 设置初始化状态
             */
            static void SetInitialized(bool initialized) { isInitialized_.store(initialized); }

        public:
            /**
             * 析构函数 - 需要public以支持unique_ptr
             */
            ~BiShareNapi();

        private:
            /**
             * 私有构造函数（单例模式）
             */
            BiShareNapi();

            // 禁用拷贝构造和赋值
            BiShareNapi(const BiShareNapi&) = delete;
            BiShareNapi& operator=(const BiShareNapi&) = delete;

            // 单例实例
            static std::unique_ptr<BiShareNapi> instance_;
            static std::atomic<bool> isInitialized_;

            // 业务管理器
            std::shared_ptr<BiShareCallbacks> callbacks_;
            std::shared_ptr<BiShareDeviceManager> deviceManager_;
            std::shared_ptr<BiShareRecordManager> recordManager_;

            // 事件回调管理 - 被bishare_callbacks.cpp使用
            struct EventInfo {
                napi_env env;
                napi_ref callbackRef;
            };
            std::map<int, std::vector<EventInfo>> eventCallbacks_;
            pthread_mutex_t eventCallbacksMutex_;

            // Friend classes
            friend class BiShareCallbacks;
            friend class BiShareDeviceManager;
            friend class BiShareRecordManager;
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_NAPI_H