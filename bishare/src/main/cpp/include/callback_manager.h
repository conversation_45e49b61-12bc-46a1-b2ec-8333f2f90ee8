#ifndef CALLBACK_MANAGER_H
#define CALLBACK_MANAGER_H

#include <napi/native_api.h>
#include <memory>
#include <map>
#include <vector>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>
#include <functional>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            /**
             * 事件信息结构
             */
            struct EventInfo {
                napi_env env;
                napi_ref callbackRef;
                bool isOnce;  // 是否为一次性监听器
            };

            /**
             * 事件数据结构
             */
            struct EventData {
                int eventType;
                std::string eventValue;
                size_t dataLength;
            };

            /**
             * 数据包数据结构
             */
            struct PacketData {
                int session;
                int bufferType;
                std::vector<uint8_t> buffer;
                int width;
                int height;
                int timestamp;
            };

            /**
             * 回调管理器 - 管理所有事件回调
             *
             * 职责：
             * 1. 管理JavaScript回调函数的注册和注销
             * 2. 处理原生事件到JavaScript的转发
             * 3. 管理事件队列和异步处理
             * 4. 提供线程安全的回调机制
             */
            class CallbackManager : public std::enable_shared_from_this<CallbackManager> {
            public:
                CallbackManager();
                ~CallbackManager();

                /**
                 * 初始化回调管理器
                 */
                bool Initialize();

                /**
                 * 释放回调管理器
                 */
                void Release();

                /**
                 * 注册事件监听器
                 */
                bool RegisterEventListener(napi_env env, int eventType, napi_value callback, bool isOnce = false);

                /**
                 * 注销事件监听器
                 */
                bool UnregisterEventListener(napi_env env, int eventType, napi_value callback);

                /**
                 * 注销所有事件监听器
                 */
                void UnregisterAllEventListeners(int eventType);

                /**
                 * 触发事件
                 */
                void EmitEvent(int eventType, const std::string& eventValue, size_t dataLength);

                /**
                 * 处理数据包回调
                 */
                void HandlePacketCallback(int session, int bufferType, const char* buffer, 
                                        int length, int width, int height, int timestamp);

                /**
                 * 静态回调函数 - 供原生服务调用
                 */
                static void OnEventCallback(int type, const char* value, int len);
                static void OnPacketCallback(int session, int type, const char* buffer, 
                                           int len, int width, int height, int time);

                /**
                 * 设置静态实例
                 */
                static void SetInstance(std::shared_ptr<CallbackManager> instance);

            private:
                // 事件监听器映射
                std::map<int, std::vector<EventInfo>> eventListeners_;
                std::mutex eventListenersMutex_;

                // 事件处理线程
                std::thread eventProcessingThread_;
                std::queue<EventData> eventQueue_;
                std::mutex eventQueueMutex_;
                std::condition_variable eventQueueCondition_;

                // 数据包处理线程
                std::thread packetProcessingThread_;
                std::queue<PacketData> packetQueue_;
                std::mutex packetQueueMutex_;
                std::condition_variable packetQueueCondition_;

                // 运行状态
                std::atomic<bool> isRunning_{false};

                // 静态实例
                static std::shared_ptr<CallbackManager> instance_;

                // 线程处理函数
                void ProcessEventQueue();
                void ProcessPacketQueue();

                // 辅助函数
                void InvokeEventCallback(const EventInfo& eventInfo, const EventData& eventData);
                void InvokePacketCallback(const EventInfo& eventInfo, const PacketData& packetData);
                napi_value CreateEventObject(napi_env env, const EventData& eventData);
                napi_value CreatePacketObject(napi_env env, const PacketData& packetData);
            };

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS

#endif // CALLBACK_MANAGER_H
