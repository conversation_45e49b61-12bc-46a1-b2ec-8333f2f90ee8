#ifndef SERVICE_MANAGER_H
#define SERVICE_MANAGER_H

#include <memory>
#include <atomic>
#include <string>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS

// 前向声明领域服务
namespace OHOS {
    namespace BiShare {
        namespace Domain {
            class DeviceService;
            class RecordingService;
            class NetworkService;
        }
    }
}

namespace OHOS {
    namespace BiShare {
        namespace Core {

            /**
             * 服务管理器 - 管理所有业务服务
             * 
             * 职责：
             * 1. 管理各个领域服务的生命周期
             * 2. 提供服务发现和访问接口
             * 3. 协调服务之间的交互
             * 4. 管理服务的初始化和释放
             */
            class ServiceManager {
            public:
                ServiceManager();
                ~ServiceManager();

                /**
                 * 初始化所有服务
                 */
                bool Initialize();

                /**
                 * 释放所有服务
                 */
                void Release();

                /**
                 * 初始化BiShare服务
                 */
                bstatus_t InitializeBiShareService(bool isConsole, bool isFile, 
                                                  const std::string& filePath, 
                                                  log_priority_t priority);

                /**
                 * 释放BiShare服务
                 */
                bstatus_t ReleaseBiShareService();

                /**
                 * 获取设备服务
                 */
                std::shared_ptr<Domain::DeviceService> GetDeviceService() const { return deviceService_; }

                /**
                 * 获取录制服务
                 */
                std::shared_ptr<Domain::RecordingService> GetRecordingService() const { return recordingService_; }

                /**
                 * 获取网络服务
                 */
                std::shared_ptr<Domain::NetworkService> GetNetworkService() const { return networkService_; }

                /**
                 * 检查服务是否已初始化
                 */
                bool IsServiceInitialized() const { return isServiceInitialized_.load(); }

            private:
                // 服务初始化状态
                std::atomic<bool> isServiceInitialized_{false};

                // 领域服务
                std::shared_ptr<OHOS::BiShare::Domain::DeviceService> deviceService_;
                std::shared_ptr<OHOS::BiShare::Domain::RecordingService> recordingService_;
                std::shared_ptr<OHOS::BiShare::Domain::NetworkService> networkService_;

                // 初始化各个服务
                bool InitializeDeviceService();
                bool InitializeRecordingService();
                bool InitializeNetworkService();
            };

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS

#endif // SERVICE_MANAGER_H
