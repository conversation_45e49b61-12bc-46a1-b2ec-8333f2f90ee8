#ifndef BISHARE_FACADE_H
#define BISHARE_FACADE_H

#include <napi/native_api.h>
#include <memory>
#include <atomic>

#include "bishare-define.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            // 前向声明
            class ServiceManager;
            class CallbackManager;

            /**
             * BiShare门面类 - 提供统一的外部接口
             * 
             * 职责：
             * 1. 作为整个BiShare模块的统一入口点
             * 2. 协调各个管理器之间的交互
             * 3. 管理模块的生命周期
             * 4. 提供简化的API接口
             */
            class BiShareFacade {
            public:
                /**
                 * 获取单例实例
                 */
                static BiShareFacade& GetInstance();

                /**
                 * 初始化门面
                 */
                bool Initialize();

                /**
                 * 释放资源
                 */
                void Release();

                /**
                 * 检查是否已初始化
                 */
                bool IsInitialized() const { return isInitialized_.load(); }

                /**
                 * 获取服务管理器
                 */
                std::shared_ptr<ServiceManager> GetServiceManager() const { return serviceManager_; }

                /**
                 * 获取回调管理器
                 */
                std::shared_ptr<CallbackManager> GetCallbackManager() const { return callbackManager_; }



                /**
                 * 设置初始化状态
                 */
                void SetInitialized(bool initialized) { isInitialized_.store(initialized); }

            private:
                BiShareFacade() = default;
                ~BiShareFacade() = default;

                // 禁止拷贝和赋值
                BiShareFacade(const BiShareFacade&) = delete;
                BiShareFacade& operator=(const BiShareFacade&) = delete;

                // 初始化状态
                std::atomic<bool> isInitialized_{false};

                // 核心管理器
                std::shared_ptr<ServiceManager> serviceManager_;
                std::shared_ptr<CallbackManager> callbackManager_;
            };

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_FACADE_H
