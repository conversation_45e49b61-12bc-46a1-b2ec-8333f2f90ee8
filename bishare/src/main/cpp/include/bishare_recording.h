#ifndef BISHARE_RECORDING_H
#define B<PERSON>HA<PERSON>_RECORDING_H

#include <napi/native_api.h>
#include <string>
#include <vector>
#include <map>
#include <memory>

#include "bishare-define.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        // BiShareRecordManager class - handles screen recording operations
        class BiShareRecordManager {
        public:
            /**
             * Constructor
             */
            BiShareRecordManager();

            /**
             * Destructor
             */
            ~BiShareRecordManager();

            /**
             * Start screen recording
             *
             * @param session Session ID
             * @param displayId Display ID
             * @param direction Recording direction
             * @return Result code
             */
            bstatus_t StartScreenRecord(int session, int displayId, int direction);

            /**
             * Stop screen recording
             *
             * @param session Session ID
             * @param displayId Display ID
             * @param direction Recording direction
             * @return Result code
             */
            bstatus_t StopScreenRecord(int session, int displayId, int direction);

            /**
             * Start capture
             *
             * @return Result code
             */
            bstatus_t StartCapture();

            /**
             * Set screen size
             *
             * @param screenWidth Screen width
             * @param screenHeight Screen height
             * @param videoWidth Video width
             * @param videoHeight Video height
             * @return Result code
             */
            bstatus_t SetSize(int screenWidth, int screenHeight, int videoWidth, int videoHeight);

            /**
             * Take a screenshot
             *
             * @param filePath Path to save the screenshot
             * @param top Top coordinate
             * @param bottom Bottom coordinate
             * @param left Left coordinate
             * @param right Right coordinate
             * @return Result code
             */
            bstatus_t Screenshot(const std::string &filePath, long top, long bottom, long left, long right);

            /**
             * Set default audio output device
             *
             * @param enable Whether to enable the default audio output device
             * @return Result code
             */
            bstatus_t SetDefaultAudioOutputDevice(bool enable);
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_RECORDING_H