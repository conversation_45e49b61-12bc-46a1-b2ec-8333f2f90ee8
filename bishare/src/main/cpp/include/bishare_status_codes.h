#ifndef BISHARE_STATUS_CODES_H
#define BISHARE_STATUS_CODES_H

#include "bishare-define.h"

/**
 * 扩展状态码定义
 *
 * 这个文件定义了一些在原始 bishare-define.h 中没有定义的状态码，
 * 这些状态码在新架构中被使用，为了保持兼容性，我们将它们映射到
 * 原始定义中的相应状态码。
 */

// ============================================================================
// 基础错误码（与原始定义保持一致）
// ============================================================================
// BS_OK = 0                    // 成功（已在bishare-define.h中定义）
// BS_NOT_INIT                  // 未初始化（已在bishare-define.h中定义）
// BS_NOT_FOUND                 // 未找到（已在bishare-define.h中定义）
// BS_PARAMS_ERROR              // 参数错误（已在bishare-define.h中定义）
// BS_OPS_ERROR                 // 操作错误（已在bishare-define.h中定义）
// BS_TIMEOUT                   // 超时（已在bishare-define.h中定义）
// BS_ALLOCATE_ERROR            // 分配错误（已在bishare-define.h中定义）
// BS_SEND_ERROR                // 发送错误（已在bishare-define.h中定义）
// BS_RECV_ERROR                // 接收错误（已在bishare-define.h中定义）

// ============================================================================
// 扩展错误码定义（映射到原始错误码）
// ============================================================================

// 通用错误（映射到原始的 BS_OPS_ERROR）
#ifndef BS_ERROR
#define BS_ERROR BS_OPS_ERROR
#endif

// 参数无效错误（映射到原始的 BS_PARAMS_ERROR）
#ifndef BS_INVALID_PARAM
#define BS_INVALID_PARAM BS_PARAMS_ERROR
#endif

// 操作不支持错误（映射到原始的 BS_NOT_FOUND）
#ifndef BS_NOT_SUPPORTED
#define BS_NOT_SUPPORTED BS_NOT_FOUND
#endif

// 权限拒绝错误（映射到原始的 BS_OPS_ERROR）
#ifndef BS_PERMISSION_DENIED
#define BS_PERMISSION_DENIED BS_OPS_ERROR
#endif

// 设备未找到错误（映射到原始的 BS_NOT_FOUND）
#ifndef BS_DEVICE_NOT_FOUND
#define BS_DEVICE_NOT_FOUND BS_NOT_FOUND
#endif

// 连接失败错误（映射到原始的 BS_OPS_ERROR）
#ifndef BS_CONNECTION_FAILED
#define BS_CONNECTION_FAILED BS_OPS_ERROR
#endif

// 内存不足错误（映射到原始的 BS_ALLOCATE_ERROR）
#ifndef BS_INSUFFICIENT_MEMORY
#define BS_INSUFFICIENT_MEMORY BS_ALLOCATE_ERROR
#endif

// ============================================================================
// 其他扩展错误码
// ============================================================================

// 已经启动错误（映射到原始的 BS_OPS_ERROR）
#ifndef BS_ALREADY_STARTED
#define BS_ALREADY_STARTED BS_OPS_ERROR
#endif

// 初始化失败错误（映射到原始的 BS_NOT_INIT）
#ifndef BS_INIT_FAILED
#define BS_INIT_FAILED BS_NOT_INIT
#endif

// 连接已关闭错误（映射到原始的 BS_OPS_ERROR）
#ifndef BS_CONNECTION_CLOSED
#define BS_CONNECTION_CLOSED BS_OPS_ERROR
#endif

// 服务未运行错误（映射到原始的 BS_NOT_INIT）
#ifndef BS_SERVICE_NOT_RUNNING
#define BS_SERVICE_NOT_RUNNING BS_NOT_INIT
#endif

// 资源不足错误（映射到原始的 BS_ALLOCATE_ERROR）
#ifndef BS_INSUFFICIENT_RESOURCES
#define BS_INSUFFICIENT_RESOURCES BS_ALLOCATE_ERROR
#endif

// 设备忙碌错误（映射到原始的 BS_OPS_ERROR）
#ifndef BS_DEVICE_BUSY
#define BS_DEVICE_BUSY BS_OPS_ERROR
#endif

// 网络错误（映射到原始的 BS_SEND_ERROR）
#ifndef BS_NETWORK_ERROR
#define BS_NETWORK_ERROR BS_SEND_ERROR
#endif

/**
 * 状态码检查宏
 */
#define IS_SUCCESS(status) ((status) == BS_OK)
#define IS_ERROR(status) ((status) != BS_OK)

/**
 * 状态码分类宏
 */
#define IS_INIT_ERROR(status) ((status) == BS_NOT_INIT || (status) == BS_INIT_FAILED)
#define IS_PARAM_ERROR(status) ((status) == BS_PARAMS_ERROR || (status) == BS_INVALID_PARAM)
#define IS_NETWORK_ERROR(status) ((status) == BS_SEND_ERROR || (status) == BS_RECV_ERROR || (status) == BS_NETWORK_ERROR)
#define IS_RESOURCE_ERROR(status) ((status) == BS_ALLOCATE_ERROR || (status) == BS_INSUFFICIENT_RESOURCES)

/**
 * 错误消息获取函数声明
 */
#ifdef __cplusplus
extern "C" {
#endif

/**
 * 获取扩展状态码的错误消息
 * @param status 状态码
 * @return 错误消息字符串
 */
const char* get_extended_error_message(bstatus_t status);

#ifdef __cplusplus
}
#endif

#endif // BISHARE_STATUS_CODES_H
