#ifndef BISHARE_LOGGER_H
#define BISHARE_LOGGER_H

#include <string>

namespace OHOS {
    namespace BiShare {

        class BiShareLogger {
        public:
            // 版本 A: 可以考虑是否让 domain 也是参数，或者像现在这样用一个类静态成员
            static void Debug(unsigned int domain, const char *tag, const char *format, ...);
            static void Info(unsigned int domain, const char *tag, const char *format, ...);
            static void Warn(unsigned int domain, const char *tag, const char *format, ...);
            static void Error(unsigned int domain, const char *tag, const char *format, ...);
            static void Fatal(unsigned int domain, const char *tag, const char *format, ...);

            // 版本 b: 如果想保持 HLogger 内部固定的 BISHARE_DOMAIN，但允许动态 tag
            static void Debug(const char *tag, const char *format, ...); // 内部使用 BISHARE_DOMAIN
            static void Info(const char *tag, const char *format, ...); // 内部使用 BISHARE_DOMAIN
            static void Warn(const char *tag, const char *format, ...); // 内部使用 BISHARE_DOMAIN
            static void Error(const char *tag, const char *format, ...); // 内部使用 BISHARE_DOMAIN
            static void Fatal(const char *tag, const char *format, ...); // 内部使用 BISHARE_DOMAIN
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_LOGGER_H