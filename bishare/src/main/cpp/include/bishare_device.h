#ifndef BISHARE_DEVICE_H
#define BISHARE_DEVICE_H

#include <napi/native_api.h>
#include <string>
#include <vector>
#include <map>
#include <memory>

#include "bishare-define.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        // Device information structure
        struct DeviceInfo {
            std::string id;      // Device ID
            std::string name;    // Device name
            std::string address; // Device address
            std::string model;   // Device model
            std::string pincode; // Device PIN code
            int status;          // Device status
            bool connected;      // Whether the device is connected
        };

        // BiShareDeviceManager class - handles device operations
        class BiShareDeviceManager {
        public:
            /**
             * Constructor
             */
            BiShareDeviceManager();

            /**
             * Destructor
             */
            ~BiShareDeviceManager();

            /**
             * Set device model
             *
             * @param model Device model
             * @return Result code
             */
            bstatus_t SetDeviceModel(const std::string &model);

            /**
             * Get device model
             *
             * @return Device model
             */
            std::string GetDeviceModel();

            /**
             * Reset device model
             *
             * @return Result code
             */
            bstatus_t ResetDeviceModel();

            /**
             * Set device information
             *
             * @param name Device name
             * @param password Device password
             * @return Result code
             */
            bstatus_t SetDeviceInfo(const std::string &name, const std::string &password);

            /**
             * Find remote device
             *
             * @param pincode Device PIN code
             * @return Result code
             */
            bstatus_t FindRemoteDevice(const std::string &pincode);

            /**
             * Get root path
             *
             * @return Root path
             */
            std::string GetRootPath();

            /**
             * Get current directory
             *
             * @return Current directory
             */
            std::string GetCurrentDirector();

            /**
             * Parse device info JSON
             *
             * @param jsonData JSON data string
             * @return Device info object
             */
            DeviceInfo ParseDeviceInfo(const std::string &jsonData);

            /**
             * Parse device info list JSON
             *
             * @param jsonData JSON data string
             * @return Vector of device info objects
             */
            std::vector<DeviceInfo> ParseDeviceInfoList(const std::string &jsonData);
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_DEVICE_H