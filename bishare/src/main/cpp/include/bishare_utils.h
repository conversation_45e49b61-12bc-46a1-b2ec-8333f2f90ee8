#ifndef BISHARE_UTILS_H
#define BISHARE_UTILS_H

#include "bishare-define.h"
#include <napi/native_api.h>
#include <string>
#include <vector>

namespace OHOS {
    namespace BiShare {

        // Utility class for common operations
        class BiShareUtils {
        public:
            /**
             * Convert bool_type_t to boolean
             *
             * @param value bool_type_t value
             * @return Boolean value
             */
            static bool BoolTypeToBoolean(bool_type_t value);

            /**
             * Convert boolean to bool_type_t
             *
             * @param value Boolean value
             * @return bool_type_t value
             */
            static bool_type_t BooleanToBoolType(bool value);

            /**
             * Convert bstatus_t to error string
             *
             * @param status Status code
             * @return Error string
             */
            static std::string StatusToString(bstatus_t status);

            /**
             * Create JavaScript error from status code
             *
             * @param env NAPI environment
             * @param status Status code
             * @return NAPI error value
             */
            static napi_value CreateErrorFromStatus(napi_env env, bstatus_t status);

            /**
             * Convert NAPI value to string
             *
             * @param env NAPI environment
             * @param value NAPI value
             * @return String representation
             */
            static std::string GetStringFromNapiValue(napi_env env, napi_value value);

            /**
             * Convert NAPI value to integer
             *
             * @param env NAPI environment
             * @param value NAPI value
             * @return Integer representation
             */
            static int GetIntFromNapiValue(napi_env env, napi_value value);

            /**
             * Convert NAPI value to boolean
             *
             * @param env NAPI environment
             * @param value NAPI value
             * @return Boolean representation
             */
            static bool GetBoolFromNapiValue(napi_env env, napi_value value);

            /**
             * Check if NAPI value is of specified type
             *
             * @param env NAPI environment
             * @param value NAPI value
             * @param type NAPI value type
             * @return True if the value is of the specified type, false otherwise
             */
            static bool IsValueOfType(napi_env env, napi_value value, napi_valuetype type);

            /**
             * Create NAPI string from C++ string
             *
             * @param env NAPI environment
             * @param value C++ string
             * @return NAPI string value
             */
            static napi_value CreateNapiString(napi_env env, const std::string &value);

            /**
             * Create NAPI array from C++ vector of strings
             *
             * @param env NAPI environment
             * @param values Vector of strings
             * @return NAPI array value
             */
            static napi_value CreateNapiStringArray(napi_env env, const std::vector<std::string> &values);
        };

    } // namespace BiShare
} // namespace OHOS

#endif // BISHARE_UTILS_H