#include "async_executor.h"
#include "bishare_logger.h"

namespace OHOS {
    namespace BiShare {
        namespace Infrastructure {

            napi_value AsyncExecutor::ExecuteAsync(napi_env env, const std::string& resourceName,
                                                 ExecuteFunction executeFunc, CompleteFunction completeFunc,
                                                 void* userData) {
                napi_deferred deferred;
                napi_value promise = CreatePromise(env, &deferred);

                auto workData = new AsyncWorkData(env, resourceName);
                workData->deferred = deferred;
                workData->userData = userData;

                auto internalData = new InternalAsyncData(workData, executeFunc, completeFunc, true);

                napi_value resourceNameValue;
                napi_create_string_utf8(env, resourceName.c_str(), NAPI_AUTO_LENGTH, &resourceNameValue);

                napi_create_async_work(env, nullptr, resourceNameValue,
                    ExecuteCallback, CompleteCallback, internalData, &workData->work);

                napi_queue_async_work(env, workData->work);

                return promise;
            }

            napi_value AsyncExecutor::ExecuteAsyncWithCallback(napi_env env, napi_value callback,
                                                             const std::string& resourceName,
                                                             ExecuteFunction executeFunc, 
                                                             CompleteFunction completeFunc,
                                                             void* userData) {
                auto workData = new AsyncWorkData(env, resourceName);
                workData->userData = userData;

                // 创建回调引用
                napi_create_reference(env, callback, 1, &workData->callback);

                auto internalData = new InternalAsyncData(workData, executeFunc, completeFunc, false);

                napi_value resourceNameValue;
                napi_create_string_utf8(env, resourceName.c_str(), NAPI_AUTO_LENGTH, &resourceNameValue);

                napi_create_async_work(env, nullptr, resourceNameValue,
                    ExecuteCallback, CompleteCallback, internalData, &workData->work);

                napi_queue_async_work(env, workData->work);

                // 返回undefined
                napi_value result;
                napi_get_undefined(env, &result);
                return result;
            }

            napi_value AsyncExecutor::ExecuteAsyncAuto(napi_env env, napi_callback_info info,
                                                     const std::string& resourceName,
                                                     ExecuteFunction executeFunc,
                                                     CompleteFunction completeFunc,
                                                     void* userData) {
                // 动态获取参数数量
                size_t argc = 0;
                napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

                // 根据实际参数数量分配数组（设置合理的最大值以防止栈溢出）
                const size_t MAX_ARGS = 20; // 合理的最大参数数量
                if (argc > MAX_ARGS) {
                    BiShareLogger::Error("AsyncExecutor", "参数数量过多: %zu (最大支持: %zu)", argc, MAX_ARGS);
                    napi_value error, message;
                    napi_create_string_utf8(env, "参数数量过多", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 使用栈数组存储参数
                napi_value argv[MAX_ARGS];
                bool hasCallback = false;

                if (argc > 0) {
                    napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                    // 检查最后一个参数是否为回调函数
                    napi_valuetype valueType;
                    napi_typeof(env, argv[argc - 1], &valueType);
                    hasCallback = (valueType == napi_function);
                }

                if (hasCallback) {
                    return ExecuteAsyncWithCallback(env, argv[argc - 1], resourceName,
                                                  executeFunc, completeFunc, userData);
                } else {
                    return ExecuteAsync(env, resourceName, executeFunc, completeFunc, userData);
                }
            }

            napi_value AsyncExecutor::CreatePromise(napi_env env, napi_deferred* deferred) {
                napi_value promise;
                napi_create_promise(env, deferred, &promise);
                return promise;
            }

            void AsyncExecutor::ResolvePromise(napi_env env, napi_deferred deferred, napi_value result) {
                napi_resolve_deferred(env, deferred, result);
            }

            void AsyncExecutor::RejectPromise(napi_env env, napi_deferred deferred, const std::string& error) {
                napi_value errorValue = CreateError(env, error);
                napi_reject_deferred(env, deferred, errorValue);
            }

            void AsyncExecutor::InvokeCallback(napi_env env, napi_ref callback, napi_value error, napi_value result) {
                napi_value callbackFunc, global, returnValue;
                napi_get_reference_value(env, callback, &callbackFunc);
                napi_get_global(env, &global);

                napi_value args[2] = { error, result };
                napi_call_function(env, global, callbackFunc, 2, args, &returnValue);
            }

            napi_value AsyncExecutor::CreateError(napi_env env, const std::string& message) {
                napi_value error, errorMessage;
                napi_create_string_utf8(env, message.c_str(), NAPI_AUTO_LENGTH, &errorMessage);
                napi_create_error(env, nullptr, errorMessage, &error);
                return error;
            }

            void AsyncExecutor::ExecuteCallback(napi_env env, void* data) {
                auto internalData = static_cast<InternalAsyncData*>(data);
                try {
                    internalData->executeFunc(internalData->workData);
                } catch (const std::exception& e) {
                    internalData->workData->errorMessage = e.what();
                } catch (...) {
                    internalData->workData->errorMessage = "Unknown error occurred during execution";
                }
            }

            void AsyncExecutor::CompleteCallback(napi_env env, napi_status status, void* data) {
                auto internalData = static_cast<InternalAsyncData*>(data);
                auto workData = internalData->workData;

                try {
                    if (status == napi_ok && workData->errorMessage.empty()) {
                        // 执行成功
                        napi_value result = internalData->completeFunc(env, workData);

                        if (internalData->usePromise) {
                            ResolvePromise(env, workData->deferred, result);
                        } else if (workData->callback) {
                            napi_value nullValue;
                            napi_get_null(env, &nullValue);
                            InvokeCallback(env, workData->callback, nullValue, result);
                        }
                    } else {
                        // 执行失败
                        std::string errorMsg = workData->errorMessage.empty() ? 
                            "Async operation failed" : workData->errorMessage;

                        if (internalData->usePromise) {
                            RejectPromise(env, workData->deferred, errorMsg);
                        } else if (workData->callback) {
                            napi_value error = CreateError(env, errorMsg);
                            napi_value nullValue;
                            napi_get_null(env, &nullValue);
                            InvokeCallback(env, workData->callback, error, nullValue);
                        }
                    }
                } catch (const std::exception& e) {
                    // 完成回调中发生异常
                    if (internalData->usePromise) {
                        RejectPromise(env, workData->deferred, e.what());
                    } else if (workData->callback) {
                        napi_value error = CreateError(env, e.what());
                        napi_value nullValue;
                        napi_get_null(env, &nullValue);
                        InvokeCallback(env, workData->callback, error, nullValue);
                    }
                } catch (...) {
                    // 未知异常
                    std::string errorMsg = "Unknown error occurred in completion callback";
                    if (internalData->usePromise) {
                        RejectPromise(env, workData->deferred, errorMsg);
                    } else if (workData->callback) {
                        napi_value error = CreateError(env, errorMsg);
                        napi_value nullValue;
                        napi_get_null(env, &nullValue);
                        InvokeCallback(env, workData->callback, error, nullValue);
                    }
                }

                // 清理资源
                delete workData;
                delete internalData;
            }

        } // namespace Infrastructure
    } // namespace BiShare
} // namespace OHOS
