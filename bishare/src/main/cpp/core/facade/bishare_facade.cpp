#include "bishare_facade.h"
#include "service_manager.h"
#include "callback_manager.h"
#include "bishare_logger.h"

namespace OHOS {
    namespace BiShare {
        namespace Core {

            static constexpr const char *FACADE_TAG = "BiShareFacade";

            BiShareFacade& BiShareFacade::GetInstance() {
                static BiShareFacade instance;
                return instance;
            }

            bool BiShareFacade::Initialize() {
                if (isInitialized_.load()) {
                    BiShareLogger::Info(FACADE_TAG, "BiShareFacade已经初始化过了");
                    return true;
                }

                try {
                    BiShareLogger::Info(FACADE_TAG, "开始初始化BiShareFacade...");

                    // 创建服务管理器
                    BiShareLogger::Info(FACADE_TAG, "创建服务管理器...");
                    serviceManager_ = std::make_shared<ServiceManager>();
                    if (!serviceManager_->Initialize()) {
                        BiShareLogger::Error(FACADE_TAG, "服务管理器初始化失败");
                        return false;
                    }
                    BiShareLogger::Info(FACADE_TAG, "服务管理器初始化成功");

                    // 创建回调管理器
                    BiShareLogger::Info(FACADE_TAG, "创建回调管理器...");
                    callbackManager_ = std::make_shared<CallbackManager>();
                    if (!callbackManager_->Initialize()) {
                        BiShareLogger::Error(FACADE_TAG, "回调管理器初始化失败");
                        return false;
                    }
                    BiShareLogger::Info(FACADE_TAG, "回调管理器初始化成功");

                    isInitialized_.store(true);
                    BiShareLogger::Info(FACADE_TAG, "BiShareFacade初始化完成");
                    return true;
                } catch (const std::exception& e) {
                    BiShareLogger::Error(FACADE_TAG, "BiShareFacade初始化异常: %s", e.what());
                    // 清理已创建的资源
                    Release();
                    return false;
                }
            }

            void BiShareFacade::Release() {
                if (!isInitialized_.load()) {
                    BiShareLogger::Info(FACADE_TAG, "BiShareFacade未初始化，无需释放");
                    return;
                }

                BiShareLogger::Info(FACADE_TAG, "开始释放BiShareFacade资源...");

                // 按相反顺序释放资源
                if (callbackManager_) {
                    BiShareLogger::Info(FACADE_TAG, "释放回调管理器...");
                    callbackManager_->Release();
                    callbackManager_.reset();
                    BiShareLogger::Info(FACADE_TAG, "回调管理器释放完成");
                }

                if (serviceManager_) {
                    BiShareLogger::Info(FACADE_TAG, "释放服务管理器...");
                    serviceManager_->Release();
                    serviceManager_.reset();
                    BiShareLogger::Info(FACADE_TAG, "服务管理器释放完成");
                }

                isInitialized_.store(false);
                BiShareLogger::Info(FACADE_TAG, "BiShareFacade资源释放完成");
            }

        } // namespace Core
    } // namespace BiShare
} // namespace OHOS
