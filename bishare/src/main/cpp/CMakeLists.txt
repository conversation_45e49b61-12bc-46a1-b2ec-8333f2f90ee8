# CMake最低版本要求
cmake_minimum_required(VERSION 3.4.1)

# 项目名称和语言 (CXX代表C++)
project(bishare_napi CXX)

# 设置C++标准 (推荐使用C++17，但C++14也可以)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF) # 关闭编译器特定的扩展，以增强可移植性

# --- RPATH 设置 ---
# RPATH (运行时搜索路径) 告诉动态链接器在哪里查找共享库。
# "$ORIGIN" 是一个特殊变量，表示“包含此可执行文件或库的目录”。
# 这对于HAR包至关重要，因为所有ABI相关的.so文件通常都在同一个目录下。
set(CMAKE_SKIP_BUILD_RPATH FALSE)             # 确保在构建时设置RPATH
set(CMAKE_BUILD_WITH_INSTALL_RPATH TRUE)      # 构建时使用INSTALL_RPATH的值
set(CMAKE_INSTALL_RPATH "$ORIGIN")            # 对于安装目标，默认RPATH为$ORIGIN
# 对于macOS平台 (虽然主要针对OpenHarmony，但保持完整性)
if(APPLE)
    set(CMAKE_MACOSX_RPATH TRUE)
endif()


# --- ABI特定的预编译库路径 ---
# OHOS_ARCH 由OpenHarmony构建系统设置 (例如 arm64-v8a, armeabi-v7a)
set(PREBUILT_LIBS_BASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/libs)
set(PREBUILT_LIBS_ABI_DIR ${PREBUILT_LIBS_BASE_DIR}/${OHOS_ARCH}) # e.g., thirdparty/biservice/libs/arm64-v8a

# --- cJSON 预编译库路径 ---
set(CJSON_PREBUILT_LIBS_BASE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/libs)
set(CJSON_PREBUILT_LIBS_ABI_DIR ${CJSON_PREBUILT_LIBS_BASE_DIR}/${OHOS_ARCH})

# --- bishare_napi 的源文件列表 ---
# 使用GLOB_RECURSE自动收集所有源文件
file(GLOB_RECURSE CORE_SOURCES "core/*.cpp")
file(GLOB_RECURSE DOMAIN_SOURCES "domain/*.cpp")
file(GLOB_RECURSE INFRASTRUCTURE_SOURCES "infrastructure/*.cpp")
file(GLOB_RECURSE INTERFACES_SOURCES "interfaces/*.cpp")

# 主模块入口文件
set(MAIN_SOURCES
    bishare_module.cpp
)

# 注意：状态码文件已移动到infrastructure目录，由INFRASTRUCTURE_SOURCES自动收集

# 合并所有源文件
set(SOURCES
    ${MAIN_SOURCES}
    ${CORE_SOURCES}
    ${DOMAIN_SOURCES}
    ${INFRASTRUCTURE_SOURCES}
    ${INTERFACES_SOURCES}
)

# --- 定义你的主NAPI库 (bishare_napi) ---
add_library(bishare_napi SHARED ${SOURCES})

# 为 bishare_napi 目标显式设置 RPATH，确保它能在运行时找到同目录下的依赖
set_target_properties(bishare_napi PROPERTIES INSTALL_RPATH "$ORIGIN")


# --- 为 bishare_napi 添加头文件包含目录 ---
target_include_directories(bishare_napi PRIVATE
    # 统一的头文件目录 - 所有.h文件都在这里
    ${CMAKE_CURRENT_SOURCE_DIR}/include                           # 统一头文件目录

    # 第三方库头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/biservice/include      # bishare-service, bishare-crypto-plugin 等库的头文件
    ${CMAKE_CURRENT_SOURCE_DIR}/thirdparty/cjson/include          # cJSON 的头文件
)


# --- 定义 IMPORTED 预编译库 ---
# IMPORTED 目标告诉 CMake 这些库已经存在，不需要编译。
# 1. libbishare-service.so (共享库)
add_library(bishare-service SHARED IMPORTED)
set_target_properties(bishare-service PROPERTIES
    IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/libbishare-service.so"
)

# 2. libbishare-crypto-plugin.so (共享库)
add_library(bishare-crypto-plugin SHARED IMPORTED)
set_target_properties(bishare-crypto-plugin PROPERTIES
    IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/libbishare-crypto-plugin.so"
)

# 3. liblog4cpp.a (静态库)
add_library(log4cpp STATIC IMPORTED)
set_target_properties(log4cpp PROPERTIES
    IMPORTED_LOCATION "${PREBUILT_LIBS_ABI_DIR}/liblog4cpp.a"
)

# 4. libcjson.a (静态库)
add_library(cjson_prebuilt STATIC IMPORTED)
set_target_properties(cjson_prebuilt PROPERTIES
    IMPORTED_LOCATION "${CJSON_PREBUILT_LIBS_ABI_DIR}/libcjson.a"
)

# --- 链接库到 bishare_napi ---
# PRIVATE: 依赖项仅用于 bishare_napi 的实现，不暴露给 bishare_napi 的使用者。
target_link_libraries(bishare_napi PRIVATE
    bishare-service      # 链接 libbishare-service.so
    bishare-crypto-plugin        # 链接 libbishare-crypto-plugin.so
    log4cpp           # 链接 liblog4cpp.a
    cjson_prebuilt    # 链接预编译的 cJSON 静态库

    # OpenHarmony 系统库
    libhilog_ndk.z.so       # 日志库
    libace_napi.z.so     # NAPI框架库
)


