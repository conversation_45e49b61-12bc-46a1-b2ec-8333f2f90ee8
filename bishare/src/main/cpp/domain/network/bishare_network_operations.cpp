#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare-service.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char *NETWORK_OPS_TAG = "BiShareNetworkOps";

        // SetNetworkInfoOperation 实现

        bool SetNetworkInfoOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 4;
            napi_value argv[4];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 3) {
                BiShareLogger::Error(NETWORK_OPS_TAG, "SetNetworkInfo需要网络类型、IP地址和MAC地址参数");
                return false;
            }

            // 解析网络类型参数
            int32_t networkType;
            napi_get_value_int32(env, argv[0], &networkType);
            workData->data.intParam1 = networkType;

            // 解析IP地址参数
            size_t addrLength;
            napi_get_value_string_utf8(env, argv[1], nullptr, 0, &addrLength);
            char* addrBuffer = new char[addrLength + 1];
            napi_get_value_string_utf8(env, argv[1], addrBuffer, addrLength + 1, &addrLength);
            workData->data.stringParam1 = std::string(addrBuffer);
            delete[] addrBuffer;

            // 解析MAC地址参数
            size_t macLength;
            napi_get_value_string_utf8(env, argv[2], nullptr, 0, &macLength);
            char* macBuffer = new char[macLength + 1];
            napi_get_value_string_utf8(env, argv[2], macBuffer, macLength + 1, &macLength);
            workData->data.stringParam2 = std::string(macBuffer);
            delete[] macBuffer;

            // 如果有回调函数参数
            if (argc >= 4) {
                napi_valuetype valueType;
                napi_typeof(env, argv[3], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[3], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetNetworkInfoOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(NETWORK_OPS_TAG, "设置网络信息: 类型=%d, IP=%s, MAC=%s",
                workData->data.intParam1,
                workData->data.stringParam1.c_str(),
                workData->data.stringParam2.c_str());

            // 调用原生网络设置函数
            workData->result = bishare_service_set_network_info(
                static_cast<network_type_t>(workData->data.intParam1),  // 网络类型
                workData->data.stringParam1.c_str(),  // IP地址
                workData->data.stringParam2.c_str()   // MAC地址
            );

            if (workData->result == BS_OK) {
                workData->successMessage = "网络信息设置成功";
                BiShareLogger::Info(NETWORK_OPS_TAG, "网络信息设置成功");
            } else {
                workData->errorMessage = std::string("网络信息设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(NETWORK_OPS_TAG, "网络信息设置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value SetNetworkInfoOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool SetNetworkInfoOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // 动态获取参数数量
            size_t argc = 0;
            napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

            if (argc < 3) {
                BiShareLogger::Error(NETWORK_OPS_TAG, "SetNetworkInfo需要网络类型、IP地址和MAC地址参数，实际参数数量: %zu", argc);
                return false;
            }

            return true;
        }

        napi_value SetNetworkInfoOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(NETWORK_OPS_TAG, "🚀 [同步] 开始执行SetNetworkInfoOperation::ExecuteOperationSync");

                // 检查服务是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(NETWORK_OPS_TAG, "❌ [同步] BiShare服务未初始化，无法设置网络信息");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 动态获取参数数量和参数
                size_t argc = 0;
                napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

                if (argc < 3) {
                    BiShareLogger::Error(NETWORK_OPS_TAG, "❌ [同步] SetNetworkInfo需要网络类型、IP地址和MAC地址参数，实际参数数量: %zu", argc);
                    napi_value error, message;
                    napi_create_string_utf8(env, "SetNetworkInfo需要网络类型、IP地址和MAC地址参数", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 根据实际参数数量分配数组
                const size_t MAX_ARGS = 10; // 合理的最大参数数量
                napi_value argv[MAX_ARGS];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                // 解析网络类型参数
                int32_t networkType;
                napi_get_value_int32(env, argv[0], &networkType);
                BiShareLogger::Info(NETWORK_OPS_TAG, "   - 网络类型: %d", networkType);

                // 解析IP地址参数
                size_t addrLength;
                napi_get_value_string_utf8(env, argv[1], nullptr, 0, &addrLength);
                char* addrBuffer = new char[addrLength + 1];
                napi_get_value_string_utf8(env, argv[1], addrBuffer, addrLength + 1, &addrLength);
                std::string ipAddress(addrBuffer);
                delete[] addrBuffer;
                BiShareLogger::Info(NETWORK_OPS_TAG, "   - IP地址: %s", ipAddress.c_str());

                // 解析MAC地址参数
                size_t macLength;
                napi_get_value_string_utf8(env, argv[2], nullptr, 0, &macLength);
                char* macBuffer = new char[macLength + 1];
                napi_get_value_string_utf8(env, argv[2], macBuffer, macLength + 1, &macLength);
                std::string macAddress(macBuffer);
                delete[] macBuffer;
                BiShareLogger::Info(NETWORK_OPS_TAG, "   - MAC地址: %s", macAddress.c_str());

                BiShareLogger::Info(NETWORK_OPS_TAG, "🔧 [同步] 开始设置网络信息...");

                // 直接调用原生网络设置函数
                BiShareLogger::Info(NETWORK_OPS_TAG, "🔄 [同步] 调用原生服务函数 bishare_service_set_network_info...");
                bstatus_t result = bishare_service_set_network_info(
                    static_cast<network_type_t>(networkType),  // 网络类型
                    ipAddress.c_str(),  // IP地址
                    macAddress.c_str()  // MAC地址
                );

                BiShareLogger::Info(NETWORK_OPS_TAG, "📊 [同步] 原生服务网络设置结果: %d (%s)",
                    static_cast<int>(result), err2str(result));

                if (result == BS_OK) {
                    BiShareLogger::Info(NETWORK_OPS_TAG, "🎉 [同步] 网络信息设置成功");
                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    return success;
                } else {
                    std::string errorMsg = std::string("网络信息设置失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(NETWORK_OPS_TAG, "❌ [同步] 网络信息设置失败: %s", err2str(result));

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(NETWORK_OPS_TAG, "❌ [同步] 同步设置网络信息异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

    } // namespace BiShare
} // namespace OHOS
