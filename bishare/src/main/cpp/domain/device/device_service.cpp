#include "device_service.h"
#include "bishare-service.h"
#include "bishare_status_codes.h"
#include <algorithm>

namespace OHOS {
    namespace BiShare {
        namespace Domain {

            DeviceService::DeviceService() : isDiscovering_(false) {
                // 构造函数
            }

            DeviceService::~DeviceService() {
                Release();
            }

            bool DeviceService::Initialize() {
                // 初始化设备服务
                currentDevice_.deviceId = "";
                currentDevice_.deviceName = "";
                currentDevice_.deviceModel = "";
                currentDevice_.devicePassword = "";
                currentDevice_.isConnected = false;
                
                discoveredDevices_.clear();
                isDiscovering_ = false;
                
                return true;
            }

            void DeviceService::Release() {
                // 停止设备发现
                if (isDiscovering_) {
                    StopDeviceDiscovery();
                }
                
                // 清理设备列表
                discoveredDevices_.clear();
            }

            bstatus_t DeviceService::SetDeviceInfo(const std::string& deviceName, const std::string& devicePassword) {
                if (!ValidateDeviceInfo(deviceName, devicePassword)) {
                    return BS_INVALID_PARAM;
                }

                // 调用原生服务
                bstatus_t result = bishare_service_set_device_info(deviceName.c_str(), devicePassword.c_str());
                
                if (result == BS_OK) {
                    currentDevice_.deviceName = deviceName;
                    currentDevice_.devicePassword = devicePassword;
                }
                
                return result;
            }

            bstatus_t DeviceService::SetDeviceModel(const std::string& deviceModel) {
                if (!ValidateDeviceModel(deviceModel)) {
                    return BS_INVALID_PARAM;
                }

                // 调用原生服务
                bstatus_t result = bishare_service_set_device_model(deviceModel.c_str());
                
                if (result == BS_OK) {
                    currentDevice_.deviceModel = deviceModel;
                }
                
                return result;
            }

            std::string DeviceService::GetDeviceModel() const {
                return currentDevice_.deviceModel;
            }

            bstatus_t DeviceService::ResetDeviceModel() {
                // 调用原生服务
                bstatus_t result = bishare_service_reset_device_model();
                
                if (result == BS_OK) {
                    currentDevice_.deviceModel = "";
                }
                
                return result;
            }

            bstatus_t DeviceService::StartDeviceDiscovery() {
                if (isDiscovering_) {
                    return BS_OK; // 已经在发现中
                }

                // 调用原生服务
                bstatus_t result = bishare_service_discovery_device();
                
                if (result == BS_OK) {
                    isDiscovering_ = true;
                }
                
                return result;
            }

            bstatus_t DeviceService::StopDeviceDiscovery() {
                if (!isDiscovering_) {
                    return BS_OK; // 已经停止
                }

                // 这里可以添加停止发现的逻辑
                isDiscovering_ = false;
                return BS_OK;
            }

            bstatus_t DeviceService::ClearDiscoveredDevices() {
                // 调用原生服务
                bstatus_t result = bishare_service_clear_discovery_device();
                
                if (result == BS_OK) {
                    discoveredDevices_.clear();
                }
                
                return result;
            }

            bstatus_t DeviceService::GetDiscoveredDevices() {
                // 调用原生服务获取设备列表
                return bishare_service_get_discovery_device();
            }

            bstatus_t DeviceService::FindRemoteDevice(const std::string& pincode) {
                if (pincode.empty()) {
                    return BS_INVALID_PARAM;
                }

                // 调用原生服务
                return bishare_service_find_remote_device(pincode.c_str());
            }

            bstatus_t DeviceService::ConnectToDevice(const std::string& deviceId, const std::string& password) {
                if (deviceId.empty()) {
                    return BS_INVALID_PARAM;
                }

                // 这里可以添加连接设备的逻辑
                // 目前原生服务中没有直接的连接API，所以返回成功
                UpdateDeviceStatus(deviceId, true);
                return BS_OK;
            }

            bstatus_t DeviceService::DisconnectFromDevice(const std::string& deviceId) {
                if (deviceId.empty()) {
                    return BS_INVALID_PARAM;
                }

                // 这里可以添加断开连接的逻辑
                UpdateDeviceStatus(deviceId, false);
                return BS_OK;
            }

            DeviceInfo DeviceService::GetCurrentDeviceInfo() const {
                return currentDevice_;
            }

            std::vector<DiscoveredDevice> DeviceService::GetDiscoveredDeviceList() const {
                return discoveredDevices_;
            }

            bool DeviceService::IsDeviceConnected(const std::string& deviceId) const {
                auto it = std::find_if(discoveredDevices_.begin(), discoveredDevices_.end(),
                    [&deviceId](const DiscoveredDevice& device) {
                        return device.deviceId == deviceId;
                    });
                
                return it != discoveredDevices_.end() && it->isAvailable;
            }

            bool DeviceService::ValidateDeviceInfo(const std::string& deviceName, const std::string& devicePassword) const {
                // 验证设备名称
                if (deviceName.empty() || deviceName.length() > 255) {
                    return false;
                }

                // 验证密码（可以为空）
                if (devicePassword.length() > 255) {
                    return false;
                }

                return true;
            }

            bool DeviceService::ValidateDeviceModel(const std::string& deviceModel) const {
                // 验证设备型号
                if (deviceModel.empty() || deviceModel.length() > 255) {
                    return false;
                }

                return true;
            }

            void DeviceService::UpdateDeviceStatus(const std::string& deviceId, bool isConnected) {
                auto it = std::find_if(discoveredDevices_.begin(), discoveredDevices_.end(),
                    [&deviceId](DiscoveredDevice& device) {
                        return device.deviceId == deviceId;
                    });
                
                if (it != discoveredDevices_.end()) {
                    it->isAvailable = isConnected;
                }
            }

        } // namespace Domain
    } // namespace BiShare
} // namespace OHOS
