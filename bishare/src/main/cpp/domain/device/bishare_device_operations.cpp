#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare_device.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char *DEVICE_OPS_TAG = "BiShareDeviceOps";

        // DiscoverDevicesOperation 实现

        bool DiscoverDevicesOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void DiscoverDevicesOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "开始发现设备...");

            // 调用原生设备发现函数
            workData->result = bishare_service_discovery_device();

            BiShareLogger::Info(DEVICE_OPS_TAG, "设备发现结果: %d", static_cast<int>(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备发现启动成功";
            } else {
                workData->errorMessage = std::string("设备发现失败: ") + 
                    std::string(err2str(workData->result));
            }
        }

        napi_value DiscoverDevicesOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool DiscoverDevicesOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // 同步模式不需要解析回调参数
            return true;
        }

        napi_value DiscoverDevicesOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(DEVICE_OPS_TAG, "同步开始发现设备...");

                // 检查服务是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(DEVICE_OPS_TAG, "BiShare服务未初始化");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 直接调用原生设备发现函数
                bstatus_t result = bishare_service_discovery_device();

                BiShareLogger::Info(DEVICE_OPS_TAG, "同步设备发现结果: %d", static_cast<int>(result));

                if (result == BS_OK) {
                    // 成功：返回true
                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    BiShareLogger::Info(DEVICE_OPS_TAG, "同步设备发现成功");
                    return success;
                } else {
                    // 失败：返回错误
                    std::string errorMsg = std::string("设备发现失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(DEVICE_OPS_TAG, "同步设备发现失败: %s", errorMsg.c_str());

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "同步设备发现异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // GetDiscoveredDevicesOperation 实现

        bool GetDiscoveredDevicesOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void GetDiscoveredDevicesOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取已发现的设备...");

            // 调用原生函数获取设备列表
            workData->result = bishare_service_get_discovery_device();

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取设备列表结果: %d", static_cast<int>(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "获取设备列表成功";
            } else {
                workData->errorMessage = std::string("获取设备列表失败: ") + 
                    std::string(err2str(workData->result));
            }
        }

        napi_value GetDiscoveredDevicesOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool GetDiscoveredDevicesOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // 同步模式不需要解析回调参数，只需要验证其他参数
            size_t argc = 10;
            napi_value argv[10];
            napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

            // 对于GetDiscoveredDevices，同步模式不需要额外参数
            return true;
        }

        napi_value GetDiscoveredDevicesOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(DEVICE_OPS_TAG, "同步获取已发现的设备...");

                // 检查服务是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(DEVICE_OPS_TAG, "BiShare服务未初始化");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 直接调用原生函数获取设备列表
                bstatus_t result = bishare_service_get_discovery_device();

                BiShareLogger::Info(DEVICE_OPS_TAG, "同步获取设备列表结果: %d", static_cast<int>(result));

                if (result == BS_OK) {
                    // 成功：返回设备列表（这里简化为返回true，实际项目中应该返回设备数组）
                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    BiShareLogger::Info(DEVICE_OPS_TAG, "同步获取设备列表成功");
                    return success;
                } else {
                    // 失败：返回错误
                    std::string errorMsg = std::string("获取设备列表失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(DEVICE_OPS_TAG, "同步获取设备列表失败: %s", errorMsg.c_str());

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "同步获取设备列表异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // SetDeviceModelOperation 实现

        bool SetDeviceModelOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 1) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "SetDeviceModel需要设备模型参数");
                return false;
            }

            // 解析设备模型参数
            size_t modelLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &modelLength);
            char* modelBuffer = new char[modelLength + 1];
            napi_get_value_string_utf8(env, argv[0], modelBuffer, modelLength + 1, &modelLength);
            workData->data.stringParam1 = std::string(modelBuffer);
            delete[] modelBuffer;

            // 如果有回调函数参数
            if (argc >= 2) {
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[1], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetDeviceModelOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "设置设备模型: %s", workData->data.stringParam1.c_str());

            // 通过设备管理器设置设备模型
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->SetDeviceModel(workData->data.stringParam1);

            if (workData->result == BS_OK) {
                workData->successMessage = "设备模型设置成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "设备模型设置成功");
            } else {
                workData->errorMessage = std::string("设备模型设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "设备模型设置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value SetDeviceModelOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool SetDeviceModelOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // 动态获取参数数量
            size_t argc = 0;
            napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

            if (argc < 1) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "SetDeviceModel需要设备模型参数，实际参数数量: %zu", argc);
                return false;
            }

            return true;
        }

        napi_value SetDeviceModelOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(DEVICE_OPS_TAG, "🚀 [同步] 开始执行SetDeviceModelOperation::ExecuteOperationSync");

                // 检查服务是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] BiShare服务未初始化，无法设置设备模型");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 动态获取参数数量和参数
                size_t argc = 0;
                napi_get_cb_info(env, info, &argc, nullptr, nullptr, nullptr);

                if (argc < 1) {
                    BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] SetDeviceModel需要设备模型参数，实际参数数量: %zu", argc);
                    napi_value error, message;
                    napi_create_string_utf8(env, "SetDeviceModel需要设备模型参数", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                // 根据实际参数数量分配数组
                const size_t MAX_ARGS = 10; // 合理的最大参数数量
                napi_value argv[MAX_ARGS];
                napi_get_cb_info(env, info, &argc, argv, nullptr, nullptr);

                // 解析设备模型字符串
                size_t modelLength;
                napi_get_value_string_utf8(env, argv[0], nullptr, 0, &modelLength);
                char* modelBuffer = new char[modelLength + 1];
                napi_get_value_string_utf8(env, argv[0], modelBuffer, modelLength + 1, &modelLength);
                std::string deviceModel(modelBuffer);
                delete[] modelBuffer;

                BiShareLogger::Info(DEVICE_OPS_TAG, "🔧 [同步] 开始设置设备模型: %s", deviceModel.c_str());

                // 通过设备管理器设置设备模型
                BiShareLogger::Info(DEVICE_OPS_TAG, "🔄 [同步] 调用设备管理器设置设备模型...");
                auto deviceManager = GetDeviceManager();
                bstatus_t result = deviceManager->SetDeviceModel(deviceModel);

                BiShareLogger::Info(DEVICE_OPS_TAG, "📊 [同步] 设备模型设置结果: %d (%s)",
                    static_cast<int>(result), err2str(result));

                if (result == BS_OK) {
                    BiShareLogger::Info(DEVICE_OPS_TAG, "🎉 [同步] 设备模型设置成功");
                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    return success;
                } else {
                    std::string errorMsg = std::string("设备模型设置失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] 设备模型设置失败: %s", err2str(result));

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] 同步设置设备模型异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // GetDeviceModelOperation 实现

        bool GetDeviceModelOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void GetDeviceModelOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取设备模型...");

            // 通过设备管理器获取设备模型
            auto deviceManager = GetDeviceManager();
            std::string model = deviceManager->GetDeviceModel();
            
            workData->result = BS_OK;
            workData->data.stringParam1 = model;
            workData->successMessage = "获取设备模型成功";
            
            BiShareLogger::Info(DEVICE_OPS_TAG, "获取设备模型成功: %s", model.c_str());
        }

        napi_value GetDeviceModelOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            napi_value result;
            napi_create_object(env, &result);
            
            napi_value successValue;
            napi_get_boolean(env, workData->result == BS_OK, &successValue);
            napi_value modelValue;
            napi_create_string_utf8(env, workData->data.stringParam1.c_str(), NAPI_AUTO_LENGTH, &modelValue);
            napi_value messageValue;
            napi_create_string_utf8(env, workData->successMessage.c_str(), NAPI_AUTO_LENGTH, &messageValue);
            
            napi_set_named_property(env, result, "success", successValue);
            napi_set_named_property(env, result, "model", modelValue);
            napi_set_named_property(env, result, "message", messageValue);
            
            return result;
        }

        // 同步执行方法实现
        bool GetDeviceModelOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // 同步模式不需要额外参数
            return true;
        }

        napi_value GetDeviceModelOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(DEVICE_OPS_TAG, "🚀 [同步] 开始执行GetDeviceModelOperation::ExecuteOperationSync");

                // 检查服务是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] BiShare服务未初始化，无法获取设备模型");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                BiShareLogger::Info(DEVICE_OPS_TAG, "🔧 [同步] 开始获取设备模型...");

                // 通过设备管理器获取设备模型
                BiShareLogger::Info(DEVICE_OPS_TAG, "🔄 [同步] 调用设备管理器获取设备模型...");
                auto deviceManager = GetDeviceManager();
                std::string model = deviceManager->GetDeviceModel();

                BiShareLogger::Info(DEVICE_OPS_TAG, "📊 [同步] 获取设备模型成功: %s", model.c_str());

                // 创建并返回结果
                napi_value result;
                napi_create_string_utf8(env, model.c_str(), NAPI_AUTO_LENGTH, &result);
                BiShareLogger::Info(DEVICE_OPS_TAG, "🎉 [同步] 设备模型获取完成");
                return result;

            } catch (const std::exception& e) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] 同步获取设备模型异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // ResetDeviceModelOperation 实现

        bool ResetDeviceModelOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void ResetDeviceModelOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "重置设备模型...");

            // 通过设备管理器重置设备模型
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->ResetDeviceModel();

            if (workData->result == BS_OK) {
                workData->successMessage = "设备模型重置成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "设备模型重置成功");
            } else {
                workData->errorMessage = std::string("设备模型重置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "设备模型重置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value ResetDeviceModelOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // SetDeviceInfoOperation 实现

        bool SetDeviceInfoOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 3;
            napi_value argv[3];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 2) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "SetDeviceInfo需要设备名称和密码参数");
                return false;
            }

            // 解析设备名称参数
            size_t nameLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &nameLength);
            char* nameBuffer = new char[nameLength + 1];
            napi_get_value_string_utf8(env, argv[0], nameBuffer, nameLength + 1, &nameLength);
            workData->data.stringParam1 = std::string(nameBuffer);
            delete[] nameBuffer;

            // 解析密码参数
            size_t passwordLength;
            napi_get_value_string_utf8(env, argv[1], nullptr, 0, &passwordLength);
            char* passwordBuffer = new char[passwordLength + 1];
            napi_get_value_string_utf8(env, argv[1], passwordBuffer, passwordLength + 1, &passwordLength);
            workData->data.stringParam2 = std::string(passwordBuffer);
            delete[] passwordBuffer;

            // 如果有回调函数参数
            if (argc >= 3) {
                napi_valuetype valueType;
                napi_typeof(env, argv[2], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[2], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetDeviceInfoOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "设置设备信息，名称: %s", workData->data.stringParam1.c_str());

            // 通过设备管理器设置设备信息
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->SetDeviceInfo(workData->data.stringParam1, workData->data.stringParam2);

            if (workData->result == BS_OK) {
                workData->successMessage = "设备信息设置成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "设备信息设置成功");
            } else {
                workData->errorMessage = std::string("设备信息设置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "设备信息设置失败: %s",
                    err2str(workData->result));
            }
        }

        napi_value SetDeviceInfoOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // FindRemoteDeviceOperation 实现

        bool FindRemoteDeviceOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 1) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "FindRemoteDevice需要PIN码参数");
                return false;
            }

            // 解析PIN码参数
            size_t pincodeLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &pincodeLength);
            char* pincodeBuffer = new char[pincodeLength + 1];
            napi_get_value_string_utf8(env, argv[0], pincodeBuffer, pincodeLength + 1, &pincodeLength);
            workData->data.stringParam1 = std::string(pincodeBuffer);
            delete[] pincodeBuffer;

            // 如果有回调函数参数
            if (argc >= 2) {
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[1], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void FindRemoteDeviceOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "查找远程设备，PIN码: %s", workData->data.stringParam1.c_str());

            // 通过设备管理器查找远程设备
            auto deviceManager = GetDeviceManager();
            workData->result = deviceManager->FindRemoteDevice(workData->data.stringParam1);

            if (workData->result == BS_OK) {
                workData->successMessage = "远程设备查找启动成功";
                BiShareLogger::Info(DEVICE_OPS_TAG, "远程设备查找启动成功");
            } else {
                workData->errorMessage = std::string("远程设备查找失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(DEVICE_OPS_TAG, "远程设备查找失败: %s",
                    err2str(workData->result));
            }
        }

        napi_value FindRemoteDeviceOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // GetRootPathOperation 实现

        napi_value GetRootPathOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取根路径...");

            // 通过设备管理器获取根路径
            auto deviceManager = BiShareNapi::GetInstance()->GetDeviceManager();
            std::string rootPath = deviceManager->GetRootPath();

            if (!rootPath.empty()) {
                BiShareLogger::Info(DEVICE_OPS_TAG, "获取根路径成功: %s", rootPath.c_str());
                napi_value result;
                napi_create_string_utf8(env, rootPath.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "获取根路径失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool GetRootPathOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            return true;
        }

        void GetRootPathOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
        }

        napi_value GetRootPathOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // GetCurrentDirectorOperation 实现

        napi_value GetCurrentDirectorOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取当前目录...");

            // 通过设备管理器获取当前目录
            auto deviceManager = BiShareNapi::GetInstance()->GetDeviceManager();
            std::string currentDir = deviceManager->GetCurrentDirector();

            if (!currentDir.empty()) {
                BiShareLogger::Info(DEVICE_OPS_TAG, "获取当前目录成功: %s", currentDir.c_str());
                napi_value result;
                napi_create_string_utf8(env, currentDir.c_str(), NAPI_AUTO_LENGTH, &result);
                return result;
            } else {
                napi_value error, message;
                napi_create_string_utf8(env, "获取当前目录失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        bool GetCurrentDirectorOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            return true;
        }

        void GetCurrentDirectorOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
        }

        napi_value GetCurrentDirectorOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        // ClearDiscoveredDevicesOperation 实现

        bool ClearDiscoveredDevicesOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void ClearDiscoveredDevicesOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "清除已发现设备列表...");

            // 调用原生清除设备列表函数
            workData->result = bishare_service_clear_discovery_device();

            BiShareLogger::Info(DEVICE_OPS_TAG, "清除设备列表结果: %d", static_cast<int>(workData->result));

            if (workData->result == BS_OK) {
                workData->successMessage = "设备列表清除成功";
            } else {
                workData->errorMessage = std::string("设备列表清除失败: ") +
                    std::string(err2str(workData->result));
            }
        }

        napi_value ClearDiscoveredDevicesOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // 同步执行方法实现
        bool ClearDiscoveredDevicesOperation::ParseArgumentsSync(napi_env env, napi_callback_info info) {
            // 同步模式不需要解析回调参数
            return true;
        }

        napi_value ClearDiscoveredDevicesOperation::ExecuteOperationSync(napi_env env, napi_callback_info info) {
            try {
                BiShareLogger::Info(DEVICE_OPS_TAG, "🚀 [同步] 开始执行ClearDiscoveredDevicesOperation::ExecuteOperationSync");

                // 检查服务是否已初始化
                if (!BiShareNapi::IsInitialized()) {
                    BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] BiShare服务未初始化，无法清除设备列表");
                    napi_value error, message;
                    napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

                BiShareLogger::Info(DEVICE_OPS_TAG, "🔧 [同步] 开始清除已发现设备列表...");

                // 直接调用原生清除设备列表函数
                BiShareLogger::Info(DEVICE_OPS_TAG, "🔄 [同步] 调用原生服务函数 bishare_service_clear_discovery_device...");
                bstatus_t result = bishare_service_clear_discovery_device();

                BiShareLogger::Info(DEVICE_OPS_TAG, "📊 [同步] 原生服务清除结果: %d (%s)",
                    static_cast<int>(result), err2str(result));

                if (result == BS_OK) {
                    BiShareLogger::Info(DEVICE_OPS_TAG, "🎉 [同步] 设备列表清除成功");
                    napi_value success;
                    napi_get_boolean(env, true, &success);
                    return success;
                } else {
                    std::string errorMsg = std::string("设备列表清除失败: ") + std::string(err2str(result));
                    BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] 设备列表清除失败: %s", err2str(result));

                    napi_value error, message;
                    napi_create_string_utf8(env, errorMsg.c_str(), NAPI_AUTO_LENGTH, &message);
                    napi_create_error(env, nullptr, message, &error);
                    return error;
                }

            } catch (const std::exception& e) {
                BiShareLogger::Error(DEVICE_OPS_TAG, "❌ [同步] 同步清除设备列表异常: %s", e.what());
                napi_value error, message;
                napi_create_string_utf8(env, e.what(), NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

        // GetCurrentDirectoryOperation 实现

        bool GetCurrentDirectoryOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            // 同步操作中不使用，但需要提供实现
            return true;
        }

        void GetCurrentDirectoryOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用，但需要提供实现
        }

        napi_value GetCurrentDirectoryOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            // 同步操作中不使用，但需要提供实现
            napi_value result;
            napi_get_undefined(env, &result);
            return result;
        }

        napi_value GetCurrentDirectoryOperation::Execute(napi_env env, napi_callback_info info) {
            // 这是一个同步操作
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            // 检查服务是否已初始化
            if (!BiShareNapi::IsInitialized()) {
                napi_value error, message;
                napi_create_string_utf8(env, "BiShare服务未初始化", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }

            BiShareLogger::Info(DEVICE_OPS_TAG, "获取当前目录...");

            // 调用原生获取当前目录函数
            const char* currentDir = bishare_service_get_current_director();

            if (currentDir != nullptr) {
                BiShareLogger::Info(DEVICE_OPS_TAG, "获取当前目录成功: %s", currentDir);
                napi_value napiResult;
                napi_create_string_utf8(env, currentDir, NAPI_AUTO_LENGTH, &napiResult);
                return napiResult;
            } else {
                BiShareLogger::Error(DEVICE_OPS_TAG, "获取当前目录失败");
                napi_value error, message;
                napi_create_string_utf8(env, "获取当前目录失败", NAPI_AUTO_LENGTH, &message);
                napi_create_error(env, nullptr, message, &error);
                return error;
            }
        }

    } // namespace BiShare
} // namespace OHOS
