#include "bishare_operation_impls.h"
#include "bishare_napi.h"
#include "bishare_logger.h"
#include "bishare_utils.h"
#include "bishare_recording.h"

namespace OHOS {
    namespace BiShare {

        static constexpr const char *RECORD_OPS_TAG = "BiShareRecordOps";

        // StartCaptureOperation 实现

        bool StartCaptureOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 1;
            napi_value argv[1];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            // 如果有回调函数参数
            if (argc >= 1) {
                napi_valuetype valueType;
                napi_typeof(env, argv[0], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[0], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void StartCaptureOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(RECORD_OPS_TAG, "开始启动捕获...");

            // 通过录制管理器启动捕获
            auto recordManager = GetRecordManager();
            workData->result = recordManager->StartCapture();

            if (workData->result == BS_OK) {
                workData->successMessage = "捕获启动成功";
                BiShareLogger::Info(RECORD_OPS_TAG, "捕获启动成功");
            } else {
                workData->errorMessage = std::string("捕获启动失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(RECORD_OPS_TAG, "捕获启动失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value StartCaptureOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // SetSizeOperation 实现

        bool SetSizeOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 5;
            napi_value argv[5];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 4) {
                BiShareLogger::Error(RECORD_OPS_TAG, "SetSize需要4个尺寸参数");
                return false;
            }

            // 解析屏幕宽度
            int32_t screenWidth;
            napi_get_value_int32(env, argv[0], &screenWidth);
            workData->data.intParam1 = screenWidth;

            // 解析屏幕高度
            int32_t screenHeight;
            napi_get_value_int32(env, argv[1], &screenHeight);
            workData->data.intParam2 = screenHeight;

            // 解析视频宽度
            int32_t videoWidth;
            napi_get_value_int32(env, argv[2], &videoWidth);
            workData->data.intParam3 = videoWidth;

            // 解析视频高度
            int32_t videoHeight;
            napi_get_value_int32(env, argv[3], &videoHeight);
            workData->data.intParam4 = videoHeight;

            // 如果有回调函数参数
            if (argc >= 5) {
                napi_valuetype valueType;
                napi_typeof(env, argv[4], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[4], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetSizeOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(RECORD_OPS_TAG, "设置尺寸，屏幕: %dx%d, 视频: %dx%d", 
                workData->data.intParam1, workData->data.intParam2,
                workData->data.intParam3, workData->data.intParam4);

            // 通过录制管理器设置尺寸
            auto recordManager = GetRecordManager();
            workData->result = recordManager->SetSize(
                workData->data.intParam1, workData->data.intParam2,
                workData->data.intParam3, workData->data.intParam4);

            if (workData->result == BS_OK) {
                workData->successMessage = "尺寸设置成功";
                BiShareLogger::Info(RECORD_OPS_TAG, "尺寸设置成功");
            } else {
                workData->errorMessage = std::string("尺寸设置失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(RECORD_OPS_TAG, "尺寸设置失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value SetSizeOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // ScreenshotOperation 实现

        bool ScreenshotOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 6;
            napi_value argv[6];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 5) {
                BiShareLogger::Error(RECORD_OPS_TAG, "Screenshot需要文件路径和4个坐标参数");
                return false;
            }

            // 解析文件路径参数
            size_t pathLength;
            napi_get_value_string_utf8(env, argv[0], nullptr, 0, &pathLength);
            char* pathBuffer = new char[pathLength + 1];
            napi_get_value_string_utf8(env, argv[0], pathBuffer, pathLength + 1, &pathLength);
            workData->data.stringParam1 = std::string(pathBuffer);
            delete[] pathBuffer;

            // 解析坐标参数
            int64_t top, bottom, left, right;
            napi_get_value_int64(env, argv[1], &top);
            napi_get_value_int64(env, argv[2], &bottom);
            napi_get_value_int64(env, argv[3], &left);
            napi_get_value_int64(env, argv[4], &right);
            
            workData->data.longParam1 = top;
            workData->data.longParam2 = bottom;
            workData->data.longParam3 = left;
            workData->data.longParam4 = right;

            // 如果有回调函数参数
            if (argc >= 6) {
                napi_valuetype valueType;
                napi_typeof(env, argv[5], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[5], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void ScreenshotOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(RECORD_OPS_TAG, "截图，路径: %s, 坐标: (%ld,%ld,%ld,%ld)", 
                workData->data.stringParam1.c_str(),
                workData->data.longParam1, workData->data.longParam2,
                workData->data.longParam3, workData->data.longParam4);

            // 通过录制管理器进行截图
            auto recordManager = GetRecordManager();
            workData->result = recordManager->Screenshot(
                workData->data.stringParam1,
                workData->data.longParam1, workData->data.longParam2,
                workData->data.longParam3, workData->data.longParam4);

            if (workData->result == BS_OK) {
                workData->successMessage = "截图成功";
                BiShareLogger::Info(RECORD_OPS_TAG, "截图成功");
            } else {
                workData->errorMessage = std::string("截图失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(RECORD_OPS_TAG, "截图失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value ScreenshotOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // StartScreenRecordOperation 实现

        bool StartScreenRecordOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 4;
            napi_value argv[4];
            napi_value thisArg;
            void *data;
            
            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);
            
            if (argc < 3) {
                BiShareLogger::Error(RECORD_OPS_TAG, "StartScreenRecord需要会话ID、显示ID和方向参数");
                return false;
            }

            // 解析会话ID
            int32_t session;
            napi_get_value_int32(env, argv[0], &session);
            workData->data.intParam1 = session;

            // 解析显示ID
            int32_t displayId;
            napi_get_value_int32(env, argv[1], &displayId);
            workData->data.intParam2 = displayId;

            // 解析方向
            int32_t direction;
            napi_get_value_int32(env, argv[2], &direction);
            workData->data.intParam3 = direction;

            // 如果有回调函数参数
            if (argc >= 4) {
                napi_valuetype valueType;
                napi_typeof(env, argv[3], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[3], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void StartScreenRecordOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(RECORD_OPS_TAG, "开始屏幕录制，会话: %d, 显示: %d, 方向: %d", 
                workData->data.intParam1, workData->data.intParam2, workData->data.intParam3);

            // 通过录制管理器开始屏幕录制
            auto recordManager = GetRecordManager();
            workData->result = recordManager->StartScreenRecord(
                workData->data.intParam1, workData->data.intParam2, workData->data.intParam3);

            if (workData->result == BS_OK) {
                workData->successMessage = "屏幕录制启动成功";
                BiShareLogger::Info(RECORD_OPS_TAG, "屏幕录制启动成功");
            } else {
                workData->errorMessage = std::string("屏幕录制启动失败: ") + 
                    std::string(err2str(workData->result));
                BiShareLogger::Error(RECORD_OPS_TAG, "屏幕录制启动失败: %s", 
                    err2str(workData->result));
            }
        }

        napi_value StartScreenRecordOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // StopScreenRecordOperation 实现

        bool StopScreenRecordOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 4;
            napi_value argv[4];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 3) {
                BiShareLogger::Error(RECORD_OPS_TAG, "StopScreenRecord需要会话ID、显示ID和方向参数");
                return false;
            }

            // 解析会话ID
            int32_t session;
            napi_get_value_int32(env, argv[0], &session);
            workData->data.intParam1 = session;

            // 解析显示ID
            int32_t displayId;
            napi_get_value_int32(env, argv[1], &displayId);
            workData->data.intParam2 = displayId;

            // 解析方向
            int32_t direction;
            napi_get_value_int32(env, argv[2], &direction);
            workData->data.intParam3 = direction;

            // 如果有回调函数参数
            if (argc >= 4) {
                napi_valuetype valueType;
                napi_typeof(env, argv[3], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[3], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void StopScreenRecordOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(RECORD_OPS_TAG, "停止屏幕录制，会话: %d, 显示: %d, 方向: %d",
                workData->data.intParam1, workData->data.intParam2, workData->data.intParam3);

            // 通过录制管理器停止屏幕录制
            auto recordManager = GetRecordManager();
            workData->result = recordManager->StopScreenRecord(
                workData->data.intParam1, workData->data.intParam2, workData->data.intParam3);

            if (workData->result == BS_OK) {
                workData->successMessage = "屏幕录制停止成功";
                BiShareLogger::Info(RECORD_OPS_TAG, "屏幕录制停止成功");
            } else {
                workData->errorMessage = std::string("屏幕录制停止失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(RECORD_OPS_TAG, "屏幕录制停止失败: %s",
                    err2str(workData->result));
            }
        }

        napi_value StopScreenRecordOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

        // SetDefaultAudioOutputDeviceOperation 实现

        bool SetDefaultAudioOutputDeviceOperation::ParseArguments(napi_env env, napi_callback_info info, AsyncWorkData* workData) {
            size_t argc = 2;
            napi_value argv[2];
            napi_value thisArg;
            void *data;

            napi_get_cb_info(env, info, &argc, argv, &thisArg, &data);

            if (argc < 1) {
                BiShareLogger::Error(RECORD_OPS_TAG, "SetDefaultAudioOutputDevice需要启用标志参数");
                return false;
            }

            // 解析启用标志
            bool enable;
            napi_get_value_bool(env, argv[0], &enable);
            workData->data.boolParam1 = enable;

            // 如果有回调函数参数
            if (argc >= 2) {
                napi_valuetype valueType;
                napi_typeof(env, argv[1], &valueType);
                if (valueType == napi_function) {
                    napi_create_reference(env, argv[1], 1, &workData->callbackRef);
                }
            }

            return true;
        }

        void SetDefaultAudioOutputDeviceOperation::ExecuteOperation(napi_env env, AsyncWorkData* workData) {
            // 检查服务是否已初始化
            if (!CheckServiceInitialized(workData)) {
                return;
            }

            BiShareLogger::Info(RECORD_OPS_TAG, "设置默认音频输出设备，启用: %s",
                workData->data.boolParam1 ? "是" : "否");

            // 通过录制管理器设置默认音频输出设备
            auto recordManager = GetRecordManager();
            workData->result = recordManager->SetDefaultAudioOutputDevice(workData->data.boolParam1);

            if (workData->result == BS_OK) {
                workData->successMessage = "默认音频输出设备设置成功";
                BiShareLogger::Info(RECORD_OPS_TAG, "默认音频输出设备设置成功");
            } else {
                workData->errorMessage = std::string("默认音频输出设备设置失败: ") +
                    std::string(err2str(workData->result));
                BiShareLogger::Error(RECORD_OPS_TAG, "默认音频输出设备设置失败: %s",
                    err2str(workData->result));
            }
        }

        napi_value SetDefaultAudioOutputDeviceOperation::CreateResult(napi_env env, AsyncWorkData* workData) {
            return CreateStandardResult(env, workData);
        }

    } // namespace BiShare
} // namespace OHOS
