/*
 * @Description: SDK API
 * @Autor: wangr
 * @Date: 2023-04-25 15:05:15
 * @LastEditors: wangr
 * @LastEditTime: 2023-05-26 16:46:58
 */
#pragma once
#ifndef _BISHARESDK_BISHARE_DEFINE_H
#define _BISHARESDK_BISHARE_DEFINE_H

#define VERSION				"3.0.20240625"

typedef enum bihare_status {
	BS_OK = 0,				// Everything's swell
	BS_NO_ERROR = 0,    	// No errors
	BS_NOT_INIT,  			// Not Initializing or initialize error
	BS_NOT_FOUND,  			// Not found or surpport
	BS_PARAMS_ERROR, 		// Invalid parameters
	BS_LIBRARY_ERROR, 		// Load library error
	BS_OPS_ERROR, 			// Operation error
	BS_RELEASE_ERROR, 		// Release error
	BS_TIMEOUT, 			// Timeout
	BS_ALLOCATE_ERROR, 		// Allocate error
	BS_OPEN_ERROR, 			// Open error
	BS_SEND_ERROR,			// Send error
	BS_RECV_ERROR,			// Recv error
 	BS_MAX
} bstatus_t;

static const char *errors_str[] = {
	"No error",                         			//AE_NO
	"Not Initializing or initialize error",
	"Not found or surpport",
	"Invalid parameters",
	"Load library error",
	"Operation error",
	"Release error",
	"Operation timeout",
	"Allocate error",
	"Open error",
	"Send error",
	"Recv error",
};

#define err2str(e) e < BS_MAX ? errors_str[e] : "unknown"

#define BSERROR_CHECK(err) if(err != AE_NO) return err

typedef enum share_event_id {
	EVENT_DEVICE_INFO = 1,			// 设备信息
	EVENT_DEVICE_STATUS = 2,		// 设备状态
	EVENT_CONNECT_STATUS = 3,		// 连接状态
	EVENT_KEY_VALUE = 4,			// KEY_VALUE信息
	EVENT_ERROR_INFO = 5,			// 错误信息
	EVENT_MEDIA_CENTER = 6,			// 媒体信息
	EVENT_SCREEN_RECORD = 7,		// 投屏控制
	EVENT_FILE_ACTION = 8,			// 文件操作
    EVENT_WFD_EVENT = 9,			// WifiDisplay状态
    EVENT_ABILITY_ACTION = 10, 		// 虚拟设备能力
	EVENT_DEVICE_INFO_LIST = 11,	// 设备信息列表
	EVENT_WINDOWS_INFO_LIST = 12,	// 窗口信息列表
	EVENT_MONITORS_INFO_LIST = 13,	// 显示器信息列表
	EVENT_LOG_INFO = 99				// 日志事件
} share_event_id;

typedef enum connect_status {
	ST_CONNECT = 1,
	ST_DISCONNECT = 2
} connect_status_t;

typedef enum session_status {
	SS_DISCONNECT = -1,
	SS_CONNECTING = -2
} session_status_t;

typedef enum direction {
	DIR_NULL = 0, 	// 未录屏或播放录屏
	DIR_SEND = 1, 	// 本机录屏给远端
	DIR_RECV = 2	// 远端录屏给本机
} direction_t;

typedef enum device_role {
	ROLE_MASTER = 1,
	ROLE_SLAVE = 2
} device_role_t;

typedef enum screen_record {
	RECORD_SCREEN_START = 0,// 屏幕录制接收
	RECORD_SCREEN_ON = 1,// 屏幕录制开启
	RECORD_SCREEN_OFF = 2,// 屏幕录制关闭
	RECORD_CAPTURE = 3,// 屏幕截屏
	RECORD_MODIFY = 4,// 录屏调整
	RECORD_CAMERA_ON = 5,// 相机录制开启
	RECORD_CAMERA_OFF = 6,// 相机录制关闭

	RECORD_TYPE_AVC = 1,
	RECORD_TYPE_HEVC = 2,
	RECORD_TYPE_NORMAL = 3,// 录屏正常
	RECORD_TYPE_SMOOTH = 4,// 录屏流畅
	RECORD_TYPE_COMPRESS = 5,// 录屏压缩
} screen_record_t;

typedef enum device_status {
	DEV_ADD = 1,
	DEV_REMOVE,
	DEV_UPDATE,
	DEV_SAME
} device_status_t;

typedef enum device_bean_status {
	DB_INIT,
	DB_CONNECTED,
	DB_RECORD,
	DB_REMOVE
} device_bean_status_t;

typedef enum log_priority {
	LOG_EMERG, 
	LOG_FATAL, 
	LOG_ALERT, 
	LOG_CRIT, 
	LOG_ERROR, 
	LOG_WARN, 
	LOG_NOTICE, 
	LOG_INFO, 
	LOG_DEBUG
} log_priority_t;

typedef enum bool_type {
	BOOL_FALSE,
	BOOL_TRUE
} bool_type_t;

typedef enum network_type {
	NotNetwork,
	Ethernet,
	Wlan,
} network_type_t;

typedef enum screen_state {
	SCREEN_OFF = 0,
	SCREEN_ON = 1
} screen_state_t;

typedef enum buffer_type {
	TYPE_VIDEO = 1,
	TYPE_AUDIO
} buffer_type_t;

typedef enum action_event_type {
	ACTION_EVENT_DOWN = 0,
	ACTION_EVENT_UP = 1,
	ACTION_EVENT_MOVE = 2,
	ACTION_EVENT_CANCEL = 3,
	ACTION_EVENT_POINTER_DOWN = 5,
	ACTION_EVENT_POINTER_UP = 6,
} action_event_type_t;

#define RECORD_WIDTH			1920 //1920
#define RECORD_HEIGHT			1080 //1080 //1072

#define RECORD_INDEX_ALL		"all"					// 选择所有
#define MIME_TYPE_AVC			"video/avc" 			// H.264 Advanced Video Coding
#define MIME_TYPE_HEVC			"video/hevc" 			// H.265 Advanced Video Coding
// error type
#define TYPE_RECORD				"record"				// 录制错误
#define TYPE_CONNECT			"connect"				// 连接错误
#define TYPE_CONNECT_NUM		"connect_num"			// 连接数已满错误
#define TYPE_SPACE				"space"					// 空间不足错误
#define TYPE_METHOD				"method"				// 方法未找到错误
#define TYPE_FOUND				"found" 				// 设备发现错误
#define TYPE_CODEC				"codec" 				// 编解码器错误
#define TYPE_ILLEGAL_STATE		"illegal_state" 		// 非法状态错误
#define TYPE_WINDOW_NOT_EXIST	"window_not_exist"		// 窗口不存在 
#define TYPE_WINDOW_NOT_MAX		"window_not_max"		// 窗口未最大化

#define TYPE_SERVICE_INIT		"service_init"
#define TYPE_AP_STATUS			"ap_status"
#define TYPE_PINCODE_UPDATE		"pincode"
#define TYPE_WINDOWS_UPDATE		"windows"
#define TYPE_MONITORS_UPDATE	"monitors"

#define MSG_SUCCESS				"success"
#define MSG_FAIL				"fail"

#define MSG_ON					"on"
#define MSG_OFF					"off"

#define ILLEGAL_ADDR			"1.1.1.1"				//无效地址
#define ILLEGAL_MAC				"11:22:33:44:55:66"		//无效MAC

#define DEFAULT_DIR_PATH			"/BoeShare"
#define DEFAULT_TMP_PATH			"/Tmp"
#define DEFAULT_LOG_PATH			"/Logs"
#define DEFAULT_CRASH_PATH			"/Logs/Crash"
#define DEFAULT_DOWNLOAD_PATH		"/Download"
#define DEFAULT_CONFIG_PATH			"/Config"
#define DEFAULT_FILE_CONFIG			"/bishare-config.json"
#define DEFAULT_FILE_LOG			"/bishare-log.txt"

#define WFD_MODE_ADD		"add"
#define WFD_MODE_UPDATED	"update"


typedef void (*OnEventCallback)(int type, const char* value, int len);//
typedef void (*OnPacketCallback)(int session, int type, const char* buffer, int len, int width, int height, int time);



#endif //_BISHARESDK_BISHARE_DEFINE_H
