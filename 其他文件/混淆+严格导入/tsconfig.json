{
  "compilerOptions": {
    "target": "ES2021",
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "outDir": "./build",
    "rootDir": "./src",
    
    // 路径映射 - 强制使用标准导入
    "baseUrl": ".",
    "paths": {
      "@ohos/libbishare_napi": ["../bishare/src/main/ets/index"],
      "@ohos/*": ["oh_modules/@ohos/*"],
      // 禁止直接访问bishare目录
      "**/bishare/**": ["./forbidden-path-placeholder"]
    },
    
    // 类型检查选项
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  },
  
  "include": [
    "src/**/*"
  ],
  
  "exclude": [
    "node_modules",
    "oh_modules",
    "build",
    "**/*.spec.ts",
    "**/*.test.ts"
  ],
  
  // 自定义编译器选项
  "ts-node": {
    "compilerOptions": {
      "module": "CommonJS"
    }
  }
}
