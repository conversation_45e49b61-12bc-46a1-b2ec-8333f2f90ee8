# 通过Obfuscation规则限制导入的完整配置

## 🎯 目标

通过OpenHarmony的混淆(obfuscation)规则来阻止相对路径导入bishare模块，强制使用标准的`@ohos/libbishare_napi`导入方式。

## 📁 配置文件结构

```
entry/
├── obfuscation-rules.txt              # 主要混淆规则
├── import-restriction-rules.txt       # 专用导入限制规则
├── build-profile.json5               # 构建配置
└── OBFUSCATION_IMPORT_RESTRICTION.md # 本说明文档
```

## ⚙️ 配置详解

### 1. build-profile.json5配置

```json5
{
  "buildOption": {
    "arkOptions": {
      "obfuscation": {
        "ruleOptions": {
          "enable": true,
          "files": [
            "./obfuscation-rules.txt",
            "./import-restriction-rules.txt"
          ]
        }
      }
    }
  },
  "buildOptionSet": [
    {
      "name": "debug",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": [
              "./obfuscation-rules.txt",
              "./import-restriction-rules.txt"
            ]
          }
        }
      }
    },
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": [
              "./obfuscation-rules.txt",
              "./import-restriction-rules.txt"
            ]
          }
        }
      }
    }
  ]
}
```

### 2. obfuscation-rules.txt核心规则

```bash
# 基本配置
-disable-obfuscation

# 保持标准模块导入
-keep-global-name: @ohos/libbishare_napi
-keep-global-name: NetworkInfoOptions
-keep-global-name: NetworkType
-keep-global-name: BiShareManager

# 混淆bishare相关路径
-obfuscate-property-name: bishare
-obfuscate-property-name: Index
-obfuscate-property-name: BiShareTypes

# 保护标准导入路径
-keep-property-name: ohos
-keep-property-name: libbishare_napi
```

### 3. import-restriction-rules.txt专用规则

```bash
# 文件路径限制
-obfuscate-file-name: **/bishare/**
-obfuscate-file-name: ../bishare/**
-obfuscate-file-name: ../../bishare/**
-obfuscate-file-name: ../../../bishare/**
-obfuscate-file-name: ../../../../bishare/**
-obfuscate-file-name: ../../../../../bishare/**

# 混淆相对路径标识符
-obfuscate-property-name: "../"
-obfuscate-property-name: "../../"
-obfuscate-property-name: "../../../"
```

## 🔧 工作原理

### 原理1: 路径混淆
混淆规则会将相对路径中的关键标识符（如`bishare`、`Index`等）进行混淆，使得相对路径导入在编译时无法正确解析。

### 原理2: 选择性保护
保护标准导入路径`@ohos/libbishare_napi`不被混淆，确保正确的导入方式仍然可用。

### 原理3: 编译时检查
当使用相对路径导入时，混淆后的路径无法找到对应的模块，编译器会报错：
```
Cannot find module '../../../../../bishare/Index'
```

## ❌ 被阻止的导入方式

```typescript
// ❌ 这些导入会在编译时失败
import { NetworkInfoOptions } from '../../../../../bishare/Index';
import { NetworkType } from '../../bishare/src/main/ets/interfaces/BiShareTypes';
import { BiShareManager } from '../../../bishare/src/main/ets/core/BiShareManager';
import { DeviceInfo } from '../../../../bishare/src/main/ets/interfaces/BiShareTypes';
```

## ✅ 允许的导入方式

```typescript
// ✅ 这些导入正常工作
import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';
import { BiShareManager } from '@ohos/libbishare_napi';
import { DeviceInfo } from '@ohos/libbishare_napi';
import { InitOptions, BlogPriority } from '@ohos/libbishare_napi';
```

## 🧪 测试验证

### 1. 创建测试文件
```typescript
// test-import-restriction.ets
import { NetworkInfoOptions } from '../../../../../bishare/Index'; // 应该失败
import { NetworkType } from '@ohos/libbishare_napi'; // 应该成功
```

### 2. 执行编译测试
```bash
cd entry
../hvigorw assembleHap --mode module -p module=entry
```

### 3. 预期结果
- 包含相对路径导入的文件编译失败
- 使用标准导入的文件编译成功

## 🔍 故障排除

### 问题1: 混淆规则不生效

**原因**: build-profile.json5配置错误
**解决方案**:
```bash
# 检查配置文件语法
cat entry/build-profile.json5 | jq .

# 确保obfuscation.ruleOptions.enable为true
# 确保files数组包含正确的规则文件路径
```

### 问题2: 标准导入也被阻止

**原因**: 过度混淆，保护规则不足
**解决方案**:
```bash
# 在obfuscation-rules.txt中添加更多保护规则
-keep-global-name: @ohos/libbishare_napi
-keep-property-name: ohos
-keep-property-name: libbishare_napi
```

### 问题3: 编译性能下降

**原因**: 混淆规则过于复杂
**解决方案**:
```bash
# 简化规则，只保留核心限制
-disable-obfuscation
-obfuscate-property-name: bishare
-keep-global-name: @ohos/libbishare_napi
```

## 📊 配置效果

### 编译时错误示例
```bash
ERROR: ArkTS:ERROR File: /path/to/file.ets:5:30
Cannot find module '../../../../../bishare/Index' or its corresponding type declarations.

ERROR: ArkTS:ERROR File: /path/to/file.ets:6:25  
Cannot find module '../../bishare/src/main/ets/interfaces/BiShareTypes' or its corresponding type declarations.
```

### 成功编译示例
```bash
✅ 使用标准导入的文件编译成功
import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';
```

## 🎯 最佳实践

1. **渐进式启用**: 先在debug模式测试，确认无误后再启用release模式
2. **规则分离**: 将导入限制规则与其他混淆规则分开管理
3. **定期测试**: 在CI/CD中加入导入规范检查
4. **文档更新**: 及时更新团队开发规范文档

## 📚 参考资料

- [OpenHarmony ArkGuard混淆工具](https://gitee.com/openharmony/arkcompiler_ets_frontend/blob/master/arkguard/README.md)
- [HarmonyOS构建配置](https://developer.harmonyos.com/cn/docs/documentation/doc-guides/hvigor-0000001055701144)
- [ArkTS编译器配置](https://docs.openharmony.cn/pages/v4.0/zh-cn/application-dev/quick-start/arkts-get-started.md)
