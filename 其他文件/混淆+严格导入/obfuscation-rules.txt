# Define project specific obfuscation rules here.
# You can include the obfuscation configuration files in the current module's build-profile.json5.
#
# For more details, see
#   https://gitee.com/openharmony/arkcompiler_ets_frontend/blob/master/arkguard/README.md

# ============================================================================
# 导入限制配置 - 禁止相对路径导入bishare模块
# ============================================================================

# 基本混淆选项 - 只针对特定模式
-disable-obfuscation

# 保持所有标准模块导入和内部导入
-keep-global-name: @ohos/libbishare_napi
-keep-global-name: NetworkInfoOptions
-keep-global-name: NetworkType
-keep-global-name: BiShareManager
-keep-global-name: DeviceInfo
-keep-global-name: InitOptions
-keep-global-name: BlogPriority

# 保持内部相对路径导入（entry模块内部）
-keep-property-name: comm
-keep-property-name: adapters
-keep-property-name: managers
-keep-property-name: service
-keep-property-name: types
-keep-property-name: constants
-keep-property-name: utils
-keep-property-name: helper
-keep-property-name: entryability
-keep-property-name: pages

# ============================================================================
# 精确的bishare导入限制
# ============================================================================

# 只混淆跨模块的bishare相对路径导入
# 保持bishare标识符，但在特定上下文中混淆

# ============================================================================
# 编译时检查规则
# ============================================================================

# 强制使用标准导入路径
# 只允许通过@ohos/libbishare_napi导入
-keep-global-name: libbishare_napi

# 禁止的导入模式（通过保持这些路径使其在编译时可检测）
# ../bishare/
# ../../bishare/
# ../../../bishare/
# ../../../../bishare/
# ../../../../../bishare/

# ============================================================================
# 错误提示配置
# ============================================================================

# 当检测到禁止的导入时，保持相关标识符以便错误提示
-keep-property-name: "相对路径导入"
-keep-property-name: "bishare模块"
-keep-property-name: "标准导入"

# ============================================================================
# 允许的导入模式
# ============================================================================

# 明确允许的标准导入
-keep-global-name: @ohos
-keep-property-name: ohos
-keep-property-name: libbishare_napi

# ============================================================================
# 注释说明
# ============================================================================

# ❌ 禁止的导入方式：
# import { NetworkInfoOptions } from '../../../../../bishare/Index';
# import { NetworkType } from '../../bishare/src/main/ets/interfaces/BiShareTypes';
# import { BiShareManager } from '../../../bishare/src/main/ets/core/BiShareManager';

# ✅ 正确的导入方式：
# import { NetworkInfoOptions, NetworkType } from '@ohos/libbishare_napi';
# import { BiShareManager } from '@ohos/libbishare_napi';

# ============================================================================
# 高级配置选项
# ============================================================================

# 启用严格模式检查
-enable-property-obfuscation
-enable-toplevel-obfuscation

# 但保持关键导入标识符
-keep-global-name: import
-keep-global-name: from
-keep-global-name: export

# 移除调试信息但保留错误提示
-remove-log
-compact