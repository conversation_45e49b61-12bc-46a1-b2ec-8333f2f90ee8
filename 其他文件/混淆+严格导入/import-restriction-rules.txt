# ============================================================================
# 精确的bishare导入限制规则
# 只阻止跨模块的相对路径导入，保持内部导入正常
# ============================================================================

# 基础配置 - 不启用全局混淆
-disable-obfuscation

# ============================================================================
# 精确限制策略
# ============================================================================

# 策略1: 只混淆特定的bishare导入路径模式
# 使用条件混淆，只对包含bishare的跨模块导入进行处理

# 保护所有内部导入
-keep-property-name: ../
-keep-property-name: ./
-keep-property-name: comm
-keep-property-name: adapters
-keep-property-name: managers
-keep-property-name: service
-keep-property-name: types
-keep-property-name: constants
-keep-property-name: utils

# 策略2: 保护标准导入路径
-keep-global-name: @ohos/libbishare_napi
-keep-property-name: ohos
-keep-property-name: libbishare_napi

# ============================================================================
# 文件路径限制
# ============================================================================

# 混淆所有包含bishare的相对路径
-obfuscate-file-name: **/bishare/**
-obfuscate-file-name: ../bishare/**
-obfuscate-file-name: ../../bishare/**
-obfuscate-file-name: ../../../bishare/**
-obfuscate-file-name: ../../../../bishare/**
-obfuscate-file-name: ../../../../../bishare/**

# ============================================================================
# 导入语句检查
# ============================================================================

# 保持import和from关键字以便检查
-keep-global-name: import
-keep-global-name: from
-keep-global-name: export

# 混淆相对路径中的关键部分
-obfuscate-property-name: "../"
-obfuscate-property-name: "../../"
-obfuscate-property-name: "../../../"
-obfuscate-property-name: "../../../../"
-obfuscate-property-name: "../../../../../"

# ============================================================================
# 类型和接口保护
# ============================================================================

# 保护标准导出的类型和接口
-keep-global-name: NetworkInfoOptions
-keep-global-name: NetworkType
-keep-global-name: DeviceInfo
-keep-global-name: InitOptions
-keep-global-name: BlogPriority
-keep-global-name: BiShareResult
-keep-global-name: BiShareError

# ============================================================================
# 错误处理配置
# ============================================================================

# 保持错误相关的标识符以便调试
-keep-property-name: error
-keep-property-name: message
-keep-property-name: stack

# ============================================================================
# 编译时强制检查
# ============================================================================

# 通过混淆使相对路径导入失败
# 这会在编译时产生"Cannot find module"错误

# 混淆bishare目录结构
-obfuscate-property-name: src
-obfuscate-property-name: main
-obfuscate-property-name: ets
-obfuscate-property-name: interfaces
-obfuscate-property-name: core

# 但保护标准模块结构
-keep-property-name: @ohos
-keep-property-name: node_modules
-keep-property-name: oh_modules

# ============================================================================
# 高级限制策略
# ============================================================================

# 策略3: 条件混淆
# 只对包含bishare的导入路径进行混淆
-conditional-obfuscation: "import.*bishare"

# 策略4: 运行时检查
# 保持运行时检查函数
-keep-global-name: checkImportPath
-keep-global-name: validateImport

# ============================================================================
# 日志和调试
# ============================================================================

# 保留导入相关的日志以便调试
-keep-property-name: "导入限制"
-keep-property-name: "相对路径"
-keep-property-name: "标准导入"

# 移除其他调试信息
-remove-log
-compact

# ============================================================================
# 最终配置
# ============================================================================

# 启用所有相关的混淆选项
-enable-toplevel-obfuscation

# 但保持关键的全局标识符
-keep-global-name: console
-keep-global-name: require
-keep-global-name: module
-keep-global-name: exports
