# BiShare框架说明文档

## 📋 概述

BiShare是一个基于OpenHarmony平台的跨设备屏幕共享和远程控制框架，采用NAPI技术实现JavaScript与C++的桥接，提供设备发现、屏幕捕获、录制、文件传输等功能。

## 🏗️ 架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    JavaScript Layer                         │
│                   (ArkTS/JS应用层)                          │
└─────────────────────┬───────────────────────────────────────┘
                      │ NAPI接口调用
┌─────────────────────▼───────────────────────────────────────┐
│                  NAPI Interface Layer                       │
│              (bishare_module.cpp入口)                       │
│              (BiShareNapiInterface接口层)                   │
└─────────────────────┬───────────────────────────────────────┘
                      │ 门面模式调用
┌─────────────────────▼───────────────────────────────────────┐
│                   Facade Layer                              │
│                 (BiShareFacade门面)                         │
└─────────────────────┬───────────────────────────────────────┘
                      │ 管理器协调
┌─────────────────────▼───────────────────────────────────────┐
│                 Manager Layer                               │
│    ┌─────────────┬─────────────┬─────────────────────────┐   │
│    │ServiceMgr   │CallbackMgr  │   AsyncExecutor         │   │
│    └─────────────┴─────────────┴─────────────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │ 业务逻辑处理
┌─────────────────────▼───────────────────────────────────────┐
│                  Domain Layer                               │
│  ┌──────────────┬──────────────┬──────────────────────────┐ │
│  │Device Domain │Record Domain │Network Domain            │ │
│  │设备管理      │录制管理      │网络管理                  │ │
│  └──────────────┴──────────────┴──────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 第三方库调用
┌─────────────────────▼───────────────────────────────────────┐
│               Third-party Libraries                         │
│    ┌─────────────────┬─────────────────┬─────────────────┐   │
│    │libbishare-service│liblog4cpp      │libcjson         │   │
│    │核心服务库        │日志库          │JSON解析库       │   │
│    └─────────────────┴─────────────────┴─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. **NAPI接口层**
- **bishare_module.cpp**: 模块注册入口
- **BiShareNapiInterface**: JavaScript API定义和参数转换
- **AsyncWorkData**: 异步操作数据结构

#### 2. **门面层**
- **BiShareFacade**: 统一的服务入口点
- 提供单例模式访问
- 协调各个管理器的工作

#### 3. **管理器层**
- **ServiceManager**: 服务生命周期管理
- **CallbackManager**: 事件回调管理
- **AsyncExecutor**: 异步任务执行器

#### 4. **业务域层**
- **Device Domain**: 设备发现、连接、信息管理
- **Recording Domain**: 屏幕录制、截图功能
- **Network Domain**: 网络通信管理

#### 5. **基础设施层**
- **Logger**: 统一日志系统
- **Utils**: 工具函数集合
- **StatusCodes**: 错误码定义

## 🔧 技术特性

### 设计模式应用

#### 1. **门面模式 (Facade Pattern)**
```cpp
class BiShareFacade {
public:
    static BiShareFacade& GetInstance();
    bool Initialize();
    void Release();
    std::shared_ptr<CallbackManager> GetCallbackManager();
    std::shared_ptr<ServiceManager> GetServiceManager();
};
```

#### 2. **工厂模式 (Factory Pattern)**
```cpp
class BiShareOperationFactory {
public:
    enum class OperationType {
        INITIALIZE, RELEASE, DISCOVER_DEVICES, // ...
    };
    static std::unique_ptr<BiShareAsyncOperation> CreateOperation(OperationType type);
};
```

#### 3. **单例模式 (Singleton Pattern)**
```cpp
class BiShareNapi {
private:
    static std::unique_ptr<BiShareNapi> instance_;
    static std::atomic<bool> isInitialized_;
public:
    static BiShareNapi* GetInstance();
};
```

#### 4. **观察者模式 (Observer Pattern)**
```cpp
class CallbackManager {
public:
    bool RegisterEventListener(napi_env env, int eventType, napi_value callback, bool isOnce = false);
    bool UnregisterEventListener(napi_env env, int eventType, napi_value callback);
    void EmitEvent(int eventType, const std::string& eventValue, size_t dataLength);
};
```

### 异步处理机制

#### 1. **NAPI异步工作**
```cpp
struct AsyncWorkData {
    napi_env env;
    napi_deferred deferred;
    napi_async_work work;
    napi_ref callbackRef;
    bstatus_t result;
    std::string errorMessage;
    std::string successMessage;
    // 通用参数结构
    struct {
        std::string stringParam1, stringParam2, stringParam3;
        int intParam1, intParam2, intParam3, intParam4;
        bool boolParam1, boolParam2;
        long longParam1, longParam2, longParam3, longParam4;
        bool_type_t enableFlag;
        log_priority_t priority;
        network_type_t networkType;
    } data;
};
```

#### 2. **Promise支持**
- 所有异步操作返回Promise
- 支持async/await语法
- 统一的错误处理机制

#### 3. **事件驱动**
- 基于事件的异步通信
- 支持一次性和持续监听
- 线程安全的事件分发

## 📊 性能优化

### 1. **内存管理**
- 智能指针管理资源生命周期
- RAII模式确保资源释放
- 避免内存泄漏

### 2. **线程安全**
- 使用互斥锁保护共享数据
- 原子操作管理状态
- 线程池处理异步任务

### 3. **缓存机制**
- 设备信息缓存
- 操作结果缓存
- 减少重复计算

## 🔒 安全特性

### 1. **参数验证**
- 严格的输入参数检查
- 类型安全转换
- 边界条件验证

### 2. **错误处理**
- 统一的错误码系统
- 详细的错误信息
- 异常安全保证

### 3. **资源保护**
- 自动资源清理
- 防止资源泄漏
- 优雅的错误恢复

## 📈 扩展性设计

### 1. **模块化架构**
- 清晰的层次分离
- 松耦合设计
- 易于单元测试

### 2. **插件机制**
- 工厂模式支持新操作类型
- 动态注册机制
- 热插拔支持

### 3. **配置驱动**
- 外部配置文件
- 运行时参数调整
- 环境适配能力

## 🎯 核心优势

### 1. **跨平台兼容**
- 专为OpenHarmony优化
- 标准NAPI接口
- 可移植性强

### 2. **高性能**
- 原生C++实现
- 异步非阻塞设计
- 内存高效管理

### 3. **易于使用**
- 简洁的JavaScript API
- Promise/async支持
- 丰富的事件机制

### 4. **可维护性**
- 清晰的架构分层
- 完善的错误处理
- 详细的日志系统
